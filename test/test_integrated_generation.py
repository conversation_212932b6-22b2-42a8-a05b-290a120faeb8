#!/usr/bin/env python3
"""
Test script for integrated character generation
Tests the gothic UI without requiring user interaction
"""

import pygame
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from character_generation.character_generator_ui import CharacterGeneratorUI
from character_generation.character_generator import CharacterGenerator


def test_character_generation_ui():
    """Test the character generation UI components"""
    print("🧪 Testing Character Generation UI Integration...")
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((1024, 768))
    pygame.display.set_caption("One Life Isekai - Character Generation Test")
    
    # Create fonts
    font_medium = pygame.font.Font(None, 24)
    font_small = pygame.font.Font(None, 18)
    
    # Test UI creation
    try:
        char_gen_ui = CharacterGeneratorUI(screen, font_medium, font_small)
        print("✅ Character Generation UI created successfully")
        
        # Test individual components
        test_rect = pygame.Rect(100, 100, 400, 300)
        parchment = char_gen_ui.create_parchment_background(test_rect)
        print("✅ Parchment background generation works")
        
        # Test character generation without UI
        generator = CharacterGenerator()
        character = generator.generate_full_character(display=False)
        print("✅ Character generation logic works")
        
        # Test formatting functions
        gender_effects = {'beauty': 20, 'persuasion': 20}
        formatted = char_gen_ui.format_gender_effects(gender_effects)
        print(f"✅ Gender effects formatting: {formatted}")
        
        trait_effects = {'STR': 10, 'DEX': -5}
        formatted = char_gen_ui.format_trait_effects(trait_effects)
        print(f"✅ Trait effects formatting: {formatted}")
        
        print("\n🎉 All UI components working correctly!")
        print("🎮 Ready for integration with main game menu")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing UI: {e}")
        return False
    
    finally:
        pygame.quit()


def test_menu_integration():
    """Test menu system integration"""
    print("\n🔗 Testing Menu Integration...")
    
    try:
        # Test import
        from ui.menu_system import MainMenuSystem
        print("✅ Menu system import successful")
        
        # Test character data attribute
        menu = MainMenuSystem()
        if hasattr(menu, 'character_data'):
            print("✅ Character data attribute exists")
        else:
            print("❌ Character data attribute missing")
            return False
        
        print("✅ Menu integration ready")
        return True
        
    except Exception as e:
        print(f"❌ Menu integration error: {e}")
        return False


def display_sample_character():
    """Display a sample generated character"""
    print("\n📜 Sample Character Generation:")
    print("=" * 50)
    
    generator = CharacterGenerator()
    character = generator.generate_full_character(display=False)
    
    race = character['race']
    gender = character['gender']
    
    print(f"🧝 Race: {race['name']} ({race['rarity']}, {race['quality']})")
    print(f"⚧️ Gender: {gender['name']}")
    
    print("\n🧬 DNA Traits:")
    for category, trait in character['dna_traits'].items():
        quality_indicator = ""
        if trait['quality'] == 'positive':
            quality_indicator = " 🔺"
        elif trait['quality'] == 'negative':
            quality_indicator = " 🔻"
        
        print(f"  {trait['category_icon']} {trait['category_name']}: {trait['name']} ({trait['rarity']}){quality_indicator}")
    
    print("\n⚡ Key Stats:")
    stats = character['effective_stats']
    print(f"  STR: {stats.get('STR', 0)}")
    print(f"  DEX: {stats.get('DEX', 0)}")
    print(f"  INT: {stats.get('INT', 0)}")
    print(f"  Magic Affinity: {stats.get('magic_affinity', 0)}")
    print(f"  Beauty: {stats.get('beauty', 0)}")
    print(f"  Expected Lifespan: {stats.get('estimated_lifespan', 0)} years")
    
    print("=" * 50)


def main():
    """Main test function"""
    print("🌟" * 30)
    print("ONE LIFE ISEKAI - INTEGRATION TEST")
    print("🌟" * 30)
    
    success = True
    
    # Test UI components
    if not test_character_generation_ui():
        success = False
    
    # Test menu integration
    if not test_menu_integration():
        success = False
    
    # Display sample character
    display_sample_character()
    
    print("\n" + "🎯" * 30)
    if success:
        print("✅ ALL INTEGRATION TESTS PASSED!")
        print("🎮 Character generation is ready for the main game")
        print("🎭 Gothic UI effects are functional")
        print("🔗 Menu system integration complete")
        print("\n🚀 To test the full experience:")
        print("   1. Run 'python main.py'")
        print("   2. Click 'Start Game'")
        print("   3. Confirm the warning dialog")
        print("   4. Experience the dramatic character generation!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Please check the error messages above")
    
    print("🎯" * 30)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
