#!/usr/bin/env python3
"""
Test script for One Life Isekai stat, trait, and skill systems
Validates data integrity and demonstrates system capabilities
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from character_generation.stat_definitions import ALL_STATS, STAT_GROUPS, BASE_STATS, UTILITY_STATS
from character_generation.traits import ALL_TRAITS, TRAIT_CATEGORIES, TRAIT_FLAGS
from character_generation.skills import ALL_SKILLS, SKILL_CATEGORIES, SKILL_DIFFICULTY


def test_stat_system():
    """Test the stat definition system"""
    print("🧮 Testing Stat System...")
    
    # Test stat counts
    print(f"  Total stats defined: {len(ALL_STATS)}")
    print(f"  Base stats: {len(BASE_STATS)}")
    print(f"  Utility stats: {len(UTILITY_STATS)}")
    
    # Test stat groups
    total_grouped = sum(len(group) for group in STAT_GROUPS.values())
    print(f"  Stats in groups: {total_grouped}")
    
    # Validate stat structure
    errors = []
    for stat_id, stat_data in ALL_STATS.items():
        if 'name' not in stat_data:
            errors.append(f"Stat {stat_id} missing 'name'")
        if 'description' not in stat_data:
            errors.append(f"Stat {stat_id} missing 'description'")
        if 'type' not in stat_data:
            errors.append(f"Stat {stat_id} missing 'type'")
    
    if errors:
        print(f"  ❌ Stat validation errors: {len(errors)}")
        for error in errors[:3]:  # Show first 3 errors
            print(f"    - {error}")
    else:
        print("  ✅ All stats properly structured")
    
    return len(errors) == 0


def test_trait_system():
    """Test the trait definition system"""
    print("\n🧬 Testing Trait System...")
    
    # Test trait counts by category
    from character_generation.traits import DISABILITY_TRAITS, PERK_TRAITS, MUTATION_TRAITS, CONDITION_TRAITS, LATENT_TRAITS
    
    print(f"  Total traits defined: {len(ALL_TRAITS)}")
    print(f"  Disability traits: {len(DISABILITY_TRAITS)}")
    print(f"  Perk traits: {len(PERK_TRAITS)}")
    print(f"  Mutation traits: {len(MUTATION_TRAITS)}")
    print(f"  Condition traits: {len(CONDITION_TRAITS)}")
    print(f"  Latent traits: {len(LATENT_TRAITS)}")
    
    # Validate trait structure
    errors = []
    for trait_id, trait_data in ALL_TRAITS.items():
        if 'name' not in trait_data:
            errors.append(f"Trait {trait_id} missing 'name'")
        if 'description' not in trait_data:
            errors.append(f"Trait {trait_id} missing 'description'")
        if 'category' not in trait_data:
            errors.append(f"Trait {trait_id} missing 'category'")
        if 'effects' not in trait_data:
            errors.append(f"Trait {trait_id} missing 'effects'")
        if 'rarity' not in trait_data:
            errors.append(f"Trait {trait_id} missing 'rarity'")
    
    # Test trait flags
    flag_count = sum(len(traits) for traits in TRAIT_FLAGS.values())
    print(f"  Traits with flags: {flag_count}")
    
    if errors:
        print(f"  ❌ Trait validation errors: {len(errors)}")
        for error in errors[:3]:
            print(f"    - {error}")
    else:
        print("  ✅ All traits properly structured")
    
    return len(errors) == 0


def test_skill_system():
    """Test the skill definition system"""
    print("\n⚔️ Testing Skill System...")
    
    # Test skill counts by category
    from character_generation.skills import COMBAT_SKILLS, MAGIC_SKILLS, CRAFTING_SKILLS, SOCIAL_SKILLS, SURVIVAL_SKILLS, PASSIVE_SKILLS, LATENT_SKILLS
    
    print(f"  Total skills defined: {len(ALL_SKILLS)}")
    print(f"  Combat skills: {len(COMBAT_SKILLS)}")
    print(f"  Magic skills: {len(MAGIC_SKILLS)}")
    print(f"  Crafting skills: {len(CRAFTING_SKILLS)}")
    print(f"  Social skills: {len(SOCIAL_SKILLS)}")
    print(f"  Survival skills: {len(SURVIVAL_SKILLS)}")
    print(f"  Passive skills: {len(PASSIVE_SKILLS)}")
    print(f"  Latent skills: {len(LATENT_SKILLS)}")
    
    # Validate skill structure
    errors = []
    for skill_id, skill_data in ALL_SKILLS.items():
        if 'name' not in skill_data:
            errors.append(f"Skill {skill_id} missing 'name'")
        if 'description' not in skill_data:
            errors.append(f"Skill {skill_id} missing 'description'")
        if 'type' not in skill_data:
            errors.append(f"Skill {skill_id} missing 'type'")
        if 'max_level' not in skill_data:
            errors.append(f"Skill {skill_id} missing 'max_level'")
        if 'rarity' not in skill_data:
            errors.append(f"Skill {skill_id} missing 'rarity'")
    
    # Test skill difficulty distribution
    total_difficulty = sum(len(skills) for skills in SKILL_DIFFICULTY.values())
    print(f"  Skills with difficulty ratings: {total_difficulty}")
    
    if errors:
        print(f"  ❌ Skill validation errors: {len(errors)}")
        for error in errors[:3]:
            print(f"    - {error}")
    else:
        print("  ✅ All skills properly structured")
    
    return len(errors) == 0


def test_system_integration():
    """Test integration between systems"""
    print("\n🔗 Testing System Integration...")
    
    # Test stat references in traits
    stat_refs_in_traits = 0
    invalid_stat_refs = []
    
    for trait_id, trait_data in ALL_TRAITS.items():
        if 'effects' in trait_data:
            for stat_name in trait_data['effects'].keys():
                stat_refs_in_traits += 1
                if stat_name not in ALL_STATS:
                    invalid_stat_refs.append(f"Trait {trait_id} references unknown stat: {stat_name}")
    
    print(f"  Stat references in traits: {stat_refs_in_traits}")
    
    # Test stat references in skills
    stat_refs_in_skills = 0
    invalid_skill_stat_refs = []
    
    for skill_id, skill_data in ALL_SKILLS.items():
        if 'stat_requirements' in skill_data:
            for stat_name in skill_data['stat_requirements'].keys():
                stat_refs_in_skills += 1
                if stat_name not in ALL_STATS:
                    invalid_skill_stat_refs.append(f"Skill {skill_id} references unknown stat: {stat_name}")
        
        if 'effects_per_level' in skill_data:
            for stat_name in skill_data['effects_per_level'].keys():
                if stat_name not in ALL_STATS and stat_name not in ['all_combat_stats', 'all_magic_bonus']:
                    invalid_skill_stat_refs.append(f"Skill {skill_id} effect references unknown stat: {stat_name}")
    
    print(f"  Stat references in skills: {stat_refs_in_skills}")
    
    # Report integration issues
    total_errors = len(invalid_stat_refs) + len(invalid_skill_stat_refs)
    if total_errors > 0:
        print(f"  ❌ Integration errors: {total_errors}")
        for error in (invalid_stat_refs + invalid_skill_stat_refs)[:3]:
            print(f"    - {error}")
    else:
        print("  ✅ All system references valid")
    
    return total_errors == 0


def demonstrate_ai_usage():
    """Demonstrate how AI systems would use these definitions"""
    print("\n🤖 AI Usage Examples...")
    
    # Example: Generate a character with specific traits
    print("  Example Character Generation:")
    print("    Base Stats: STR=15, DEX=20, VIT=12, INT=25, SPE=18, WILL=22")
    print("    Traits: ['keen_intellect', 'arcane_blood', 'frail']")
    print("    Skills: ['spellcraft:3', 'pyromancy:5', 'alchemy:2']")
    
    # Calculate effective stats
    base_int = 25
    trait_int_bonus = 25 + 20  # keen_intellect + arcane_blood
    effective_int = base_int + trait_int_bonus
    print(f"    Effective INT: {base_int} + {trait_int_bonus} = {effective_int}")
    
    # Example: Event processing
    print("\n  Example Event Processing:")
    print("    Event: 'Magical Academy Entrance Exam'")
    print("    Requirements: INT >= 30, magic_affinity >= 20")
    print("    Character qualifies: ✅ (INT=70, magic_affinity=55)")
    
    # Example: Skill learning
    print("\n  Example Skill Learning:")
    print("    Attempting to learn 'necromancy'")
    print("    Requirements: INT >= 25, magic_affinity >= 20, WILL >= 20")
    print("    Character qualifies: ✅")
    print("    Difficulty: very_hard (requires extended training)")


def main():
    """Main test function"""
    print("=" * 60)
    print("ONE LIFE ISEKAI - SYSTEM VALIDATION")
    print("=" * 60)
    
    # Run all tests
    stat_test = test_stat_system()
    trait_test = test_trait_system()
    skill_test = test_skill_system()
    integration_test = test_system_integration()
    
    # Demonstrate AI usage
    demonstrate_ai_usage()
    
    # Final results
    print("\n" + "=" * 60)
    print("VALIDATION RESULTS:")
    print(f"  Stat System: {'✅ PASS' if stat_test else '❌ FAIL'}")
    print(f"  Trait System: {'✅ PASS' if trait_test else '❌ FAIL'}")
    print(f"  Skill System: {'✅ PASS' if skill_test else '❌ FAIL'}")
    print(f"  Integration: {'✅ PASS' if integration_test else '❌ FAIL'}")
    
    all_passed = stat_test and trait_test and skill_test and integration_test
    
    if all_passed:
        print("\n🎉 ALL SYSTEMS VALIDATED SUCCESSFULLY!")
        print("   Ready for AI-driven character generation and gameplay.")
        return 0
    else:
        print("\n❌ VALIDATION FAILED!")
        print("   Please fix the errors above before proceeding.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
