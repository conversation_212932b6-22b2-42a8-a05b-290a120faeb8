#!/usr/bin/env python3
"""
Test script for complete character generation including background history
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from character_generation.character_generator import CharacterGenerator
from character_generation.background_history_generator import Background<PERSON><PERSON>oryGenerator


def test_background_generation():
    """Test background history generation"""
    print("🧪 Testing Background History Generation...")
    
    # Generate base character
    generator = CharacterGenerator()
    character = generator.generate_full_character(display=False)
    
    print("✅ Base character generated")
    print(f"   Race: {character['race']['name']}")
    print(f"   Gender: {character['gender']['name']}")
    
    # Generate background history
    history_gen = BackgroundHistoryGenerator(character)
    background_history = history_gen.generate_full_background()
    updated_character = history_gen.get_updated_character()
    
    print(f"✅ Background history generated: {len(background_history)} events")
    
    # Display summary
    print("\n📜 BACKGROUND SUMMARY:")
    print("=" * 50)
    
    current_stage = ""
    for event in background_history:
        if event['life_stage'] != current_stage:
            current_stage = event['life_stage']
            print(f"\n🔸 {current_stage.upper()}")
            print("-" * 30)
        
        age_text = f"Age {event['age']}"
        event_text = event['event']
        
        if event['type'] == 'growth':
            effects = ", ".join([f"+{v} {k}" for k, v in event['effects'].items()])
            print(f"  {age_text}: {event_text} ({effects})")
        else:
            print(f"  {age_text}: {event_text}")
            
            if event['effects']:
                effects = ", ".join([f"{v:+d} {k}" for k, v in event['effects'].items()])
                print(f"    Effects: {effects}")
            
            if event['traits_gained']:
                traits = ", ".join(event['traits_gained'])
                print(f"    Traits: {traits}")
            
            if event['skills_gained']:
                skills = ", ".join([f"{s['name']} (Lv{s.get('level', 1)})" for s in event['skills_gained']])
                print(f"    Skills: {skills}")
    
    print("\n⚡ FINAL CHARACTER STATS:")
    print("=" * 30)
    stats = updated_character['effective_stats']
    for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']:
        print(f"  {stat}: {stats.get(stat, 0)}")
    
    print(f"\n🎭 Total Traits: {len(updated_character.get('traits', []))}")
    print(f"⚔️ Total Skills: {len(updated_character.get('skills', []))}")
    
    return True


def test_event_requirements():
    """Test event requirement checking"""
    print("\n🔍 Testing Event Requirements...")
    
    # Create a character with specific traits for testing
    generator = CharacterGenerator()
    character = generator.generate_full_character(display=False)
    
    # Force some traits for testing
    character['traits'] = [{'id': 'blind', 'name': 'Blind'}]
    character['effective_stats']['magic_affinity'] = 80
    character['effective_stats']['iq'] = 130
    
    history_gen = BackgroundHistoryGenerator(character)
    
    # Test specific event requirements
    from character_generation.life_events import MIDDLE_CHILDHOOD_EVENTS, EARLY_CHILDHOOD_EVENTS
    
    third_eye_event = MIDDLE_CHILDHOOD_EVENTS['awakened_third_eye']
    can_trigger = history_gen.check_event_requirements(third_eye_event)
    print(f"✅ Third Eye event (requires blind): {'Can trigger' if can_trigger else 'Cannot trigger'}")
    
    scholar_event = EARLY_CHILDHOOD_EVENTS['discovered_by_scholar']
    can_trigger = history_gen.check_event_requirements(scholar_event)
    print(f"✅ Scholar event (requires high IQ): {'Can trigger' if can_trigger else 'Cannot trigger'}")
    
    return True


def test_multiple_generations():
    """Test multiple character generations for variety"""
    print("\n🎲 Testing Multiple Generations...")
    
    interesting_characters = []
    
    for i in range(10):
        generator = CharacterGenerator()
        character = generator.generate_full_character(display=False)
        
        history_gen = BackgroundHistoryGenerator(character)
        background_history = history_gen.generate_full_background()
        updated_character = history_gen.get_updated_character()
        
        # Count interesting events
        special_events = [e for e in background_history if e['type'] in ['awakening', 'blessing', 'curse', 'transformation']]
        
        if len(special_events) > 0:
            interesting_characters.append({
                'character': updated_character,
                'special_events': special_events
            })
    
    print(f"✅ Generated {len(interesting_characters)} characters with special events")
    
    # Show most interesting character
    if interesting_characters:
        most_interesting = max(interesting_characters, key=lambda x: len(x['special_events']))
        char = most_interesting['character']
        events = most_interesting['special_events']
        
        print(f"\n🌟 Most Interesting Character:")
        print(f"   Race: {char['race']['name']}")
        print(f"   Gender: {char['gender']['name']}")
        print(f"   Special Events: {len(events)}")
        
        for event in events:
            print(f"     Age {event['age']}: {event['event']} ({event['type']})")
    
    return True


def main():
    """Main test function"""
    print("🌟" * 40)
    print("ONE LIFE ISEKAI - FULL CHARACTER GENERATION TEST")
    print("🌟" * 40)
    
    success = True
    
    try:
        # Test background generation
        if not test_background_generation():
            success = False
        
        # Test event requirements
        if not test_event_requirements():
            success = False
        
        # Test multiple generations
        if not test_multiple_generations():
            success = False
        
        print("\n" + "🎯" * 40)
        if success:
            print("✅ ALL TESTS PASSED!")
            print("🎮 Complete character generation system ready!")
            print("🎭 Background history system functional!")
            print("🔗 Event requirements working correctly!")
            print("\n🚀 To experience the full gothic UI:")
            print("   Run 'python main.py' and click 'Start Game'")
        else:
            print("❌ SOME TESTS FAILED!")
            print("🔧 Check error messages above")
        
        print("🎯" * 40)
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
