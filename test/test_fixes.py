#!/usr/bin/env python3
"""
Test script to verify the color fixes and race stats display
"""

import pygame
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from character_generation.character_generator_ui import CharacterGeneratorUI
from character_generation.character_generator import CharacterGenerator


def test_color_safety():
    """Test color handling with edge cases"""
    print("🎨 Testing Color Safety...")
    
    # Initialize pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    font_medium = pygame.font.Font(None, 24)
    font_small = pygame.font.Font(None, 18)
    
    try:
        ui = CharacterGeneratorUI(screen, font_medium, font_small)
        
        # Test color validation
        test_colors = [
            (255, 255, 255),  # Normal white
            (300, -50, 128),  # Out of range values
            (255.5, 128.7, 64.2),  # Float values
            ui.gold_color,  # Normal gold
            ui.blood_red,   # Normal red
            ui.ink_color    # Normal ink
        ]
        
        for i, color in enumerate(test_colors):
            try:
                # Test animate_text_reveal with various colors
                ui.animate_text_reveal(
                    f"Test color {i}", 10, 10 + i*20, 
                    font_small, color, 1.0
                )
                print(f"✅ Color {i}: {color} - Safe")
            except Exception as e:
                print(f"❌ Color {i}: {color} - Error: {e}")
        
        # Test glow border with edge cases
        test_rect = pygame.Rect(100, 100, 200, 100)
        test_intensities = [0.0, 0.5, 1.0, 1.5, -0.5]
        
        for intensity in test_intensities:
            try:
                ui.draw_glowing_border(test_rect, intensity)
                print(f"✅ Glow intensity {intensity} - Safe")
            except Exception as e:
                print(f"❌ Glow intensity {intensity} - Error: {e}")
        
        print("✅ Color safety tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Color safety test failed: {e}")
        return False
    finally:
        pygame.quit()


def test_race_stats_display():
    """Test race stats display completeness"""
    print("\n📊 Testing Race Stats Display...")
    
    generator = CharacterGenerator()
    
    # Test multiple races
    test_races = ['human', 'elf', 'dwarf', 'dragonborn']
    
    for race_id in test_races:
        # Generate character with specific race (simulate)
        from character_generation.races import RACES
        if race_id in RACES:
            race_data = RACES[race_id]
            
            print(f"\n🧝 Testing {race_data['name']}:")
            print(f"   Base Stats: {len(race_data['base_stats'])} stats")
            for stat, (min_val, max_val) in race_data['base_stats'].items():
                print(f"     {stat}: {min_val}-{max_val}")
            
            print(f"   Utility Stats: {len(race_data['utility_stats'])} stats")
            for stat, value in race_data['utility_stats'].items():
                if isinstance(value, tuple):
                    print(f"     {stat}: {value[0]}-{value[1]}")
                else:
                    print(f"     {stat}: {value}")
    
    print("✅ Race stats display test completed")
    return True


def test_character_generation_flow():
    """Test the complete character generation flow"""
    print("\n🎮 Testing Character Generation Flow...")
    
    try:
        generator = CharacterGenerator()
        
        # Test race generation
        race_data = generator.generate_race()
        print(f"✅ Race generated: {race_data['name']}")
        print(f"   Base stats count: {len(race_data['base_stats'])}")
        print(f"   Utility stats count: {len(race_data['utility_stats'])}")
        
        # Test gender generation
        gender_data = generator.generate_gender()
        print(f"✅ Gender generated: {gender_data['name']}")
        print(f"   Effects count: {len(gender_data['effects'])}")
        
        # Test DNA traits
        dna_traits = generator.generate_all_dna_traits()
        print(f"✅ DNA traits generated: {len(dna_traits)} categories")
        
        # Test background history
        from character_generation.background_history_generator import BackgroundHistoryGenerator
        history_gen = BackgroundHistoryGenerator(generator.character_profile)
        background = history_gen.generate_full_background()
        print(f"✅ Background history generated: {len(background)} events")
        
        return True
        
    except Exception as e:
        print(f"❌ Character generation flow failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🔧" * 40)
    print("ONE LIFE ISEKAI - FIXES VERIFICATION")
    print("🔧" * 40)
    
    success = True
    
    # Test color safety
    if not test_color_safety():
        success = False
    
    # Test race stats display
    if not test_race_stats_display():
        success = False
    
    # Test character generation flow
    if not test_character_generation_flow():
        success = False
    
    print("\n" + "🎯" * 40)
    if success:
        print("✅ ALL FIXES VERIFIED!")
        print("🎨 Color handling is safe")
        print("📊 Race stats display is complete")
        print("🎮 Character generation flow works")
        print("\n🚀 Ready to run the full game:")
        print("   python main.py")
    else:
        print("❌ SOME ISSUES REMAIN!")
        print("🔧 Check error messages above")
    
    print("🎯" * 40)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
