#!/usr/bin/env python3
"""
Test script for One Life Isekai character generator
Demonstrates race, gender, and DNA trait generation
"""

import sys
import json
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from character_generation.character_generator import CharacterGenerator


def test_single_generation():
    """Test a single character generation"""
    print("=" * 60)
    print("ONE LIFE ISEKAI - CHARACTER GENERATOR TEST")
    print("=" * 60)
    
    generator = CharacterGenerator()
    character = generator.generate_full_character(display=True)
    
    return character


def test_multiple_generations(count: int = 5):
    """Test multiple character generations for variety"""
    print("\n" + "=" * 60)
    print(f"GENERATING {count} CHARACTERS FOR VARIETY TEST")
    print("=" * 60)
    
    characters = []
    
    for i in range(count):
        print(f"\n--- Character {i+1} ---")
        generator = CharacterGenerator()
        character = generator.generate_full_character(display=False)
        characters.append(character)
        
        # Display summary
        race = character['race']
        gender = character['gender']
        print(f"Race: {race['name']} ({race['rarity']}, {race['quality']})")
        print(f"Gender: {gender['name']}")
        
        # Show a few key DNA traits
        dna_traits = character['dna_traits']
        print("Key DNA Traits:")
        for category, trait in list(dna_traits.items())[:3]:
            print(f"  {trait['category_name']}: {trait['name']} ({trait['rarity']})")
    
    return characters


def analyze_generation_statistics(characters):
    """Analyze generation statistics"""
    print("\n" + "=" * 60)
    print("GENERATION STATISTICS")
    print("=" * 60)
    
    # Race statistics
    race_counts = {}
    race_rarities = {}
    race_qualities = {}
    
    for char in characters:
        race = char['race']
        race_name = race['name']
        race_rarity = race['rarity']
        race_quality = race['quality']
        
        race_counts[race_name] = race_counts.get(race_name, 0) + 1
        race_rarities[race_rarity] = race_rarities.get(race_rarity, 0) + 1
        race_qualities[race_quality] = race_qualities.get(race_quality, 0) + 1
    
    print("Race Distribution:")
    for race, count in sorted(race_counts.items()):
        percentage = (count / len(characters)) * 100
        print(f"  {race}: {count} ({percentage:.1f}%)")
    
    print("\nRace Rarity Distribution:")
    for rarity, count in sorted(race_rarities.items()):
        percentage = (count / len(characters)) * 100
        print(f"  {rarity}: {count} ({percentage:.1f}%)")
    
    print("\nRace Quality Distribution:")
    for quality, count in sorted(race_qualities.items()):
        percentage = (count / len(characters)) * 100
        print(f"  {quality}: {count} ({percentage:.1f}%)")
    
    # Gender statistics
    gender_counts = {}
    for char in characters:
        gender_name = char['gender']['name']
        gender_counts[gender_name] = gender_counts.get(gender_name, 0) + 1
    
    print("\nGender Distribution:")
    for gender, count in sorted(gender_counts.items()):
        percentage = (count / len(characters)) * 100
        print(f"  {gender}: {count} ({percentage:.1f}%)")
    
    # DNA trait rarity statistics
    print("\nDNA Trait Rarity Distribution:")
    trait_rarities = {}
    
    for char in characters:
        for trait_data in char['dna_traits'].values():
            rarity = trait_data['rarity']
            trait_rarities[rarity] = trait_rarities.get(rarity, 0) + 1
    
    total_traits = sum(trait_rarities.values())
    for rarity, count in sorted(trait_rarities.items()):
        percentage = (count / total_traits) * 100
        print(f"  {rarity}: {count} ({percentage:.1f}%)")


def save_character_example(character):
    """Save a character example to JSON file"""
    filename = "character_example.json"
    
    # Convert character to JSON-serializable format
    json_character = json.dumps(character, indent=2, default=str)
    
    with open(filename, 'w') as f:
        f.write(json_character)
    
    print(f"\n💾 Character example saved to {filename}")


def display_character_summary(character):
    """Display a comprehensive character summary"""
    print("\n" + "=" * 60)
    print("FINAL CHARACTER SUMMARY")
    print("=" * 60)
    
    race = character['race']
    gender = character['gender']
    
    print(f"🧝 Race: {race['name']} ({race['rarity']}, {race['quality']})")
    print(f"⚧️ Gender: {gender['name']}")
    
    print("\n📊 Base Stats:")
    for stat, value in character['base_stats'].items():
        print(f"  {stat}: {value}")
    
    print("\n🧬 DNA Traits:")
    for category, trait in character['dna_traits'].items():
        effects = []
        for stat, value in trait['effects'].items():
            if isinstance(value, (int, float)) and abs(value) < 1:
                effects.append(f"{value:+.0%} {stat}")
            else:
                effects.append(f"{value:+d} {stat}")
        
        effects_str = ", ".join(effects) if effects else "No effects"
        print(f"  {trait['category_name']}: {trait['name']} ({effects_str})")
    
    print("\n⚡ Effective Stats (after all modifiers):")
    for stat, value in sorted(character['effective_stats'].items()):
        if isinstance(value, float):
            print(f"  {stat}: {value:.1f}")
        else:
            print(f"  {stat}: {value}")
    
    print("\n📝 Generation Log:")
    for log_entry in character['generation_log']:
        print(f"  {log_entry['step']}: {log_entry['description']}")


def main():
    """Main test function"""
    try:
        # Test single character generation
        character = test_single_generation()
        
        # Display comprehensive summary
        display_character_summary(character)
        
        # Save example
        save_character_example(character)
        
        # Test multiple generations for statistics
        print("\n" + "🔄" * 20)
        characters = test_multiple_generations(20)
        
        # Analyze statistics
        analyze_generation_statistics(characters)
        
        print("\n" + "=" * 60)
        print("✅ CHARACTER GENERATOR TEST COMPLETE!")
        print("🎮 Ready for integration with main game systems.")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Error during character generation: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
