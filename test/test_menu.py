#!/usr/bin/env python3
"""
Test script for One Life Isekai main menu
Demonstrates all features without requiring user interaction
"""

import pygame
import sys
import time
from ui.menu_system import MainMenuSystem, MenuState
from config.settings import settings


def automated_test():
    """Run automated tests of the menu system"""
    print("🧪 Running automated menu tests...")
    
    # Test settings system
    print("\n📋 Testing Settings System:")
    print(f"  Master Volume: {settings.get_master_volume()}")
    print(f"  Music Volume: {settings.get_music_volume()}")
    print(f"  SFX Volume: {settings.get_sfx_volume()}")
    print(f"  Fullscreen: {settings.get_fullscreen()}")
    print(f"  Text Speed: {settings.get_text_speed()}")
    
    # Test settings modification
    settings.set_volume('master', 85)
    settings.set_fullscreen(True)
    settings.set_text_speed('Fast')
    print("\n✅ Settings modified successfully")
    
    # Test settings persistence
    settings.save_settings()
    print("✅ Settings saved to file")
    
    # Initialize menu system
    try:
        menu = MainMenuSystem()
        print("✅ Menu system initialized successfully")
        
        # Test state transitions
        menu.current_state = MenuState.OPTIONS
        print("✅ Options menu state set")
        
        menu.current_state = MenuState.LORE
        print("✅ Lore archive state set")
        
        menu.current_state = MenuState.MAIN
        print("✅ Main menu state restored")
        
        # Cleanup
        pygame.quit()
        print("✅ Pygame cleaned up")
        
    except Exception as e:
        print(f"❌ Error during menu testing: {e}")
        return False
    
    print("\n🎉 All tests passed! Menu system is working correctly.")
    return True


def main():
    """Main test function"""
    print("=" * 60)
    print("ONE LIFE ISEKAI - MENU SYSTEM TEST")
    print("=" * 60)
    
    # Run automated tests
    if automated_test():
        print("\n🚀 Ready to run the full menu system!")
        print("   Run 'python main.py' to start the interactive menu.")
        print("\n📝 Features available:")
        print("   • Start Game (with confirmation)")
        print("   • Lore Archive (scrollable)")
        print("   • Options Menu (audio, display, gameplay)")
        print("   • Exit Game (with confirmation)")
        print("   • Settings persistence")
        print("   • Gothic dark fantasy theme")
        return 0
    else:
        print("\n❌ Tests failed. Check the error messages above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
