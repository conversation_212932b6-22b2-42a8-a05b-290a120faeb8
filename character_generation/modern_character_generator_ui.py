"""
Modern Character Generator UI for One Life Isekai
Features MMO-style character sheet with animated reveals
"""

import pygame
import time
import math
from typing import Dict, Any, Optional
from .character_generator import CharacterGenerator
from .character_logger import CharacterLogger
from .name_generator import NameGenerator
from .modern_character_sheet import ModernCharacterSheet
from utils.constants import COLOR<PERSON>, SCREEN_WIDTH, SCREEN_HEIGHT


class ModernCharacterGeneratorUI:
    def __init__(self, screen: pygame.Surface):
        self.screen = screen
        self.generator = CharacterGenerator()
        self.logger = CharacterLogger()
        self.name_generator = NameGenerator()
        
        # Load fonts
        self._load_fonts()
        
        # Create character sheet
        self.character_sheet = ModernCharacterSheet(screen, self.fonts)
        
        # Animation state
        self.current_step = 0
        self.reveal_progress = 0.0
        self.popup_alpha = 0
        self.popup_text = ""
        self.popup_subtext = ""
        self.popup_color = (255, 255, 255)
        
        # Colors
        self.bg_color = (15, 15, 25)
        self.popup_bg = (40, 40, 60)
        self.gold_color = (255, 215, 0)
        self.green_color = (50, 205, 50)
        self.red_color = (220, 20, 60)
        self.white_color = (255, 255, 255)
        
        # Character data
        self.character_data = None
    
    def _load_fonts(self):
        """Load fonts for the UI"""
        try:
            self.fonts = {
                'large': pygame.font.Font(None, 48),
                'medium': pygame.font.Font(None, 32),
                'small': pygame.font.Font(None, 24),
                'popup_large': pygame.font.Font(None, 64),
                'popup_medium': pygame.font.Font(None, 40)
            }
        except:
            # Fallback fonts
            self.fonts = {
                'large': pygame.font.Font(None, 48),
                'medium': pygame.font.Font(None, 32),
                'small': pygame.font.Font(None, 24),
                'popup_large': pygame.font.Font(None, 64),
                'popup_medium': pygame.font.Font(None, 40)
            }
    
    def run_character_generation(self) -> Optional[Dict[str, Any]]:
        """Run the complete modern character generation process"""
        try:
            # Start logging
            self.logger.start_generation()
            
            # Initialize character sheet with unreadable text
            self.character_sheet.initialize_character_sheet({})
            
            # Show initial character sheet
            self._display_initial_sheet()
            
            # Generate race
            if not self._generate_and_reveal_race():
                return None
            
            # Generate gender
            if not self._generate_and_reveal_gender():
                return None
            
            # Generate name
            if not self._generate_and_reveal_name():
                return None
            
            # Generate DNA traits
            if not self._generate_and_reveal_dna_traits():
                return None
            
            # Generate background history
            if not self._generate_background_history():
                return None
            
            # Final reveal and summary
            if not self._final_character_reveal():
                return None
            
            # Finalize logging
            self.logger.finalize_log(self.character_data)
            
            return self.character_data
            
        except Exception as e:
            print(f"Error during character generation: {e}")
            return None
    
    def _display_initial_sheet(self):
        """Display the initial character sheet with unreadable text"""
        for _ in range(60):  # Show for 1 second
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(0.0)
            
            # Draw title
            title_text = self.fonts['popup_large'].render("🌟 DESTINY AWAKENS 🌟", True, self.gold_color)
            title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, 100))
            self.screen.blit(title_text, title_rect)
            
            pygame.display.flip()
            pygame.time.wait(16)  # ~60 FPS
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        return True
    
    def _generate_and_reveal_race(self) -> bool:
        """Generate race and show animated reveal"""
        # Generate race
        race_data = self.generator.generate_race()
        self.character_data = self.generator.character_profile
        self.logger.log_race_generation(race_data)
        
        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)
        
        # Show popup reveal
        rarity_text = race_data['rarity'].title()
        quality_text = race_data['quality'].title() if race_data['quality'] != 'average' else 'Average'
        
        # Determine colors
        if race_data['quality'] == 'positive':
            quality_color = self.green_color
        elif race_data['quality'] == 'negative':
            quality_color = self.red_color
        else:
            quality_color = self.white_color
        
        # Show rarity first
        if not self._show_popup_sequence([
            (rarity_text, self.gold_color, 2.0),
            (quality_text, quality_color, 1.0),
            (f"Race: {race_data['name']}", self.white_color, 1.5)
        ]):
            return False
        
        # Reveal character sheet lines
        self._reveal_character_lines(0, 5)  # Reveal first 5 lines
        
        return True
    
    def _generate_and_reveal_gender(self) -> bool:
        """Generate gender and show animated reveal"""
        # Generate gender
        gender_data = self.generator.generate_gender()
        self.character_data = self.generator.character_profile
        self.logger.log_gender_generation(gender_data)
        
        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)
        
        # Show popup reveal
        if not self._show_popup_sequence([
            ("Gender Determined", self.gold_color, 1.5),
            (f"{gender_data['icon']} {gender_data['name']}", self.white_color, 2.0)
        ]):
            return False
        
        # Reveal more character sheet lines
        self._reveal_character_lines(5, 7)  # Reveal gender line
        
        return True
    
    def _generate_and_reveal_name(self) -> bool:
        """Generate name and show reveal"""
        # Generate name
        race_name = self.character_data['race']['name'].lower()
        gender_name = self.character_data['gender']['name'].lower()
        character_name = self.name_generator.generate_name(race_name, gender_name)
        
        self.character_data['name'] = character_name
        self.logger.log_name_generation(character_name)
        
        # Show popup reveal
        if not self._show_popup_sequence([
            ("Name Revealed", self.gold_color, 1.5),
            (character_name, self.white_color, 2.0)
        ]):
            return False
        
        return True
    
    def _show_popup_sequence(self, popup_sequence: list) -> bool:
        """Show a sequence of popup texts"""
        for popup_text, popup_color, duration in popup_sequence:
            if not self._show_popup(popup_text, popup_color, duration):
                return False
        return True
    
    def _show_popup(self, text: str, color: tuple, duration: float) -> bool:
        """Show animated popup text"""
        start_time = time.time()
        
        while time.time() - start_time < duration:
            progress = (time.time() - start_time) / duration
            
            # Calculate alpha for fade in/out
            if progress < 0.3:
                alpha = int(255 * (progress / 0.3))
            elif progress > 0.7:
                alpha = int(255 * ((1.0 - progress) / 0.3))
            else:
                alpha = 255
            
            # Draw background
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(self.reveal_progress)
            
            # Draw popup
            popup_surface = self.fonts['popup_large'].render(text, True, color)
            popup_surface.set_alpha(alpha)
            popup_rect = popup_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
            
            # Draw popup background
            bg_rect = popup_rect.inflate(40, 20)
            popup_bg_surface = pygame.Surface(bg_rect.size)
            popup_bg_surface.fill(self.popup_bg)
            popup_bg_surface.set_alpha(alpha // 2)
            self.screen.blit(popup_bg_surface, bg_rect)
            
            # Draw popup text
            self.screen.blit(popup_surface, popup_rect)
            
            pygame.display.flip()
            pygame.time.wait(16)
            
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
        
        return True
    
    def _reveal_character_lines(self, start_line: int, end_line: int):
        """Reveal character sheet lines with animation"""
        for line in range(start_line, min(end_line, len(self.character_sheet.readable_lines))):
            self.reveal_progress = line / len(self.character_sheet.readable_lines)
            
            # Show reveal animation
            for frame in range(30):  # 0.5 second animation
                self.screen.fill(self.bg_color)
                self.character_sheet.draw_character_sheet(self.reveal_progress)
                pygame.display.flip()
                pygame.time.wait(16)
                
                # Handle events
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        return False

    def _generate_and_reveal_dna_traits(self) -> bool:
        """Generate DNA traits and show reveals"""
        dna_traits = self.generator.generate_all_dna_traits()
        self.character_data = self.generator.character_profile

        # Log each trait
        for trait_data in dna_traits.values():
            self.logger.log_dna_trait_generation(trait_data)

        # Update character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)

        # Show each trait reveal
        for i, (category, trait_data) in enumerate(dna_traits.items()):
            rarity_text = trait_data['rarity'].title()
            quality_text = trait_data['quality'].title() if trait_data['quality'] != 'average' else 'Average'

            # Determine colors
            if trait_data['quality'] == 'positive':
                quality_color = self.green_color
            elif trait_data['quality'] == 'negative':
                quality_color = self.red_color
            else:
                quality_color = self.white_color

            # Show trait popup
            popup_sequence = [
                (rarity_text, self.gold_color, 1.5),
                (quality_text, quality_color, 1.0),
                (f"{trait_data['category_name']}: {trait_data['name']}", self.white_color, 2.0)
            ]

            if not self._show_popup_sequence(popup_sequence):
                return False

            # Reveal corresponding lines
            self._reveal_character_lines(7 + i * 2, 9 + i * 2)

        return True

    def _generate_background_history(self) -> bool:
        """Generate background history"""
        try:
            from .background_history_generator import BackgroundHistoryGenerator
            history_gen = BackgroundHistoryGenerator(self.character_data)
            background_history = history_gen.generate_full_background()
            self.character_data = history_gen.get_updated_character()

            # Show background generation popup
            if not self._show_popup_sequence([
                ("Weaving Life's Tapestry...", self.gold_color, 2.0),
                (f"{len(background_history)} Life Events Generated", self.white_color, 1.5)
            ]):
                return False

            return True
        except Exception as e:
            print(f"Background generation error: {e}")
            return True  # Continue even if background fails

    def _final_character_reveal(self) -> bool:
        """Final character reveal with complete sheet"""
        # Update final character sheet
        self.character_sheet.character_data = self.character_data
        self.character_sheet.update_readable_stats(self.character_data)

        # Reveal all remaining lines
        self._reveal_character_lines(0, len(self.character_sheet.readable_lines))

        # Show completion popup
        if not self._show_popup_sequence([
            ("🎭 DESTINY SEALED 🎭", self.gold_color, 3.0),
            ("Your One Life Begins...", self.white_color, 2.0)
        ]):
            return False

        # Show final character sheet for a while
        for _ in range(300):  # 5 seconds
            self.screen.fill(self.bg_color)
            self.character_sheet.draw_character_sheet(1.0)

            # Draw completion message
            complete_text = self.fonts['medium'].render("Press any key to continue...", True, self.gold_color)
            complete_rect = complete_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
            self.screen.blit(complete_text, complete_rect)

            pygame.display.flip()
            pygame.time.wait(16)

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                    return True

        return True
