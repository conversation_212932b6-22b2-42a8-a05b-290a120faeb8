"""
AI-driven character generator for One Life Isekai
Phase 1: Race, Gender, and DNA trait generation
"""

import random
import time
from typing import Dict, Any, Tu<PERSON>
from .races import RACES, RACE_RARITY_WEIGHTS, RACE_QUALITY_DESCRIPTORS
from .dna_traits import DNA_TRAIT_CATEGORIES, GENDER_OPTIONS, RARITY_DESCRIPTORS, QUALITY_DESCRIPTORS, MAGIC_ELEMENTS
from .stat_definitions import ALL_STATS


class CharacterGenerator:
    def __init__(self, logger=None):
        self.character_profile = {
            'race': {},
            'gender': {},
            'dna_traits': {},
            'base_stats': {},
            'effective_stats': {},
            'traits': [],
            'skills': [],
            'generation_log': []
        }
        self.logger = logger
    
    def weighted_random_choice(self, choices: Dict[str, Any], weight_key: str = 'weight') -> str:
        """Select random choice based on weights"""
        total_weight = sum(choice.get(weight_key, 1) for choice in choices.values())
        random_value = random.uniform(0, total_weight)
        
        current_weight = 0
        for key, choice in choices.items():
            current_weight += choice.get(weight_key, 1)
            if random_value <= current_weight:
                return key
        
        return list(choices.keys())[-1]
    
    def generate_race(self) -> Dict[str, Any]:
        """Generate character race with rarity-weighted selection"""
        # Create weighted race pool
        race_pool = {}
        for race_id, race_data in RACES.items():
            weight = RACE_RARITY_WEIGHTS[race_data['rarity']]
            race_pool[race_id] = {'weight': weight, **race_data}
        
        # Select race
        selected_race_id = self.weighted_random_choice(race_pool)
        selected_race = RACES[selected_race_id]
        
        # Generate base stats within race ranges
        base_stats = {}
        for stat, (min_val, max_val) in selected_race['base_stats'].items():
            base_stats[stat] = random.randint(min_val, max_val)
        
        # Generate utility stats
        utility_stats = {}
        for stat, value in selected_race['utility_stats'].items():
            if isinstance(value, tuple):
                utility_stats[stat] = random.randint(value[0], value[1])
            else:
                utility_stats[stat] = value
        
        race_result = {
            'id': selected_race_id,
            'name': selected_race['name'],
            'rarity': selected_race['rarity'],
            'quality': selected_race['quality'],
            'base_stats': base_stats,
            'utility_stats': utility_stats,
            'innate_traits': selected_race['innate_traits'].copy(),
            'bonus_chances': selected_race['bonus_chances'].copy(),
            'description': selected_race['description']
        }
        
        self.character_profile['race'] = race_result
        self.character_profile['base_stats'].update(base_stats)
        self.character_profile['effective_stats'].update(base_stats)
        self.character_profile['effective_stats'].update(utility_stats)
        
        self.log_generation_step('race', f"Generated {selected_race['name']} ({selected_race['rarity']}, {selected_race['quality']})")
        
        return race_result
    
    def generate_gender(self) -> Dict[str, Any]:
        """Generate character gender with weighted selection"""
        selected_gender_id = self.weighted_random_choice(GENDER_OPTIONS)
        selected_gender = GENDER_OPTIONS[selected_gender_id]
        
        gender_result = {
            'id': selected_gender_id,
            'name': selected_gender['name'],
            'icon': selected_gender['icon'],
            'effects': selected_gender['effects'].copy()
        }
        
        # Apply gender effects
        self.apply_stat_effects(selected_gender['effects'])
        
        self.character_profile['gender'] = gender_result
        self.log_generation_step('gender', f"Generated {selected_gender['name']}")
        
        return gender_result
    
    def generate_dna_trait(self, category_id: str) -> Dict[str, Any]:
        """Generate a single DNA trait from specified category"""
        category = DNA_TRAIT_CATEGORIES[category_id]

        # Select trait based on weights
        selected_trait_id = self.weighted_random_choice(category['traits'])
        selected_trait = category['traits'][selected_trait_id]

        # Generate specific values for range-based effects
        effects = {}
        for stat, value in selected_trait['effects'].items():
            if isinstance(value, tuple):
                effects[stat] = random.randint(value[0], value[1])
            else:
                effects[stat] = value

        # Handle magic elements for magic_affinity category
        magic_elements = []
        if category_id == 'magic_affinity' and 'elements' in selected_trait:
            element_count = selected_trait['elements']
            # Handle both list and integer cases
            if isinstance(element_count, list):
                magic_elements = element_count
            elif isinstance(element_count, int) and element_count > 0:
                # Select random elements
                available_elements = list(MAGIC_ELEMENTS.keys())
                magic_elements = random.sample(available_elements, min(element_count, len(available_elements)))

            # Add elemental bonuses to effects
            if magic_elements:
                for element in magic_elements:
                    element_data = MAGIC_ELEMENTS[element]
                    # Base damage and resist bonuses based on magic affinity level
                    base_bonus = max(5, effects.get('magic_affinity', 70) // 10)
                    effects[element_data['damage_stat']] = base_bonus
                    effects[element_data['resist_stat']] = base_bonus

        trait_result = {
            'category': category_id,
            'category_name': category['name'],
            'category_icon': category['icon'],
            'id': selected_trait_id,
            'name': selected_trait['name'],
            'rarity': selected_trait['rarity'],
            'quality': selected_trait['quality'],
            'effects': effects,
            'magic_elements': magic_elements  # Store selected elements
        }

        # Apply trait effects
        self.apply_stat_effects(effects)

        self.character_profile['dna_traits'][category_id] = trait_result
        self.log_generation_step('dna_trait', f"Generated {category['name']}: {selected_trait['name']} ({selected_trait['rarity']}, {selected_trait['quality']})")

        return trait_result
    
    def generate_all_dna_traits(self) -> Dict[str, Any]:
        """Generate all DNA trait categories"""
        dna_results = {}
        
        for category_id in DNA_TRAIT_CATEGORIES.keys():
            trait_result = self.generate_dna_trait(category_id)
            dna_results[category_id] = trait_result
        
        return dna_results
    
    def apply_stat_effects(self, effects: Dict[str, Any]) -> None:
        """Apply stat effects to character profile"""
        for stat, value in effects.items():
            if stat in self.character_profile['effective_stats']:
                if isinstance(value, (int, float)) and value > 0 and value < 1:
                    # Percentage modifier
                    self.character_profile['effective_stats'][stat] *= (1 + value / 100)
                else:
                    # Flat modifier
                    self.character_profile['effective_stats'][stat] += value
            else:
                # New stat
                self.character_profile['effective_stats'][stat] = value
    
    def log_generation_step(self, step_type: str, description: str) -> None:
        """Log generation step for debugging and tracking"""
        log_entry = {
            'step': step_type,
            'description': description,
            'timestamp': time.time()
        }
        self.character_profile['generation_log'].append(log_entry)
    
    def display_race_reveal(self, race_data: Dict[str, Any]) -> None:
        """Display dramatic race reveal"""
        print("\n" + "═" * 50)
        print("🔮 Origin Revealed...")
        print("─" * 30)
        
        rarity_text = RARITY_DESCRIPTORS[race_data['rarity']]
        quality_text = RACE_QUALITY_DESCRIPTORS[race_data['quality']]
        
        print(f"🔸 Rarity: {rarity_text} ({quality_text})")
        print(f"🧝 Race: {race_data['name']}")
        print("─" * 30)
        
        # Display stat ranges
        for stat, value in race_data['base_stats'].items():
            stat_info = ALL_STATS.get(stat, {})
            stat_name = stat_info.get('name', stat)
            print(f"{stat}: {value} ({self.evaluate_stat_quality(stat, value)})")
        
        print("─" * 30)
        
        # Display utility stats
        for stat, value in race_data['utility_stats'].items():
            if stat == 'height':
                print(f"Height: {value} cm")
            elif stat == 'beauty':
                print(f"Beauty: {value}")
            elif stat == 'estimated_lifespan':
                print(f"Lifespan: {value} years")
            elif stat == 'load_capacity_bonus':
                if value != 0:
                    print(f"Load Capacity Bonus: {value:+d} kg")
        
        print("─" * 30)
        
        # Display innate traits
        if race_data['innate_traits']:
            print(f"Innate Traits: {', '.join(race_data['innate_traits'])}")
        
        # Display bonus chances
        for bonus, chance in race_data['bonus_chances'].items():
            bonus_name = bonus.replace('_', ' ').title()
            print(f"Bonus: {chance}% Chance {bonus_name}")
        
        print("═" * 50)
    
    def display_gender_reveal(self, gender_data: Dict[str, Any]) -> None:
        """Display dramatic gender reveal"""
        print("\n" + "═" * 40)
        print("Your physical identity is...")
        print(f"{gender_data['icon']} {gender_data['name']}")
        
        # Display bonuses
        effects_text = []
        for stat, value in gender_data['effects'].items():
            if isinstance(value, (int, float)) and value > 0 and value < 1:
                effects_text.append(f"+{value}% {stat.title()}")
            else:
                effects_text.append(f"+{value} {stat.title()}")
        
        if effects_text:
            print(f"Bonuses applied: {', '.join(effects_text)}")
        
        print("═" * 40)
    
    def display_dna_trait_reveal(self, trait_data: Dict[str, Any]) -> None:
        """Display dramatic DNA trait reveal"""
        print("\n" + "─" * 40)

        # Build display text
        rarity_text = RARITY_DESCRIPTORS[trait_data['rarity']]
        quality_text = QUALITY_DESCRIPTORS[trait_data['quality']]

        # Format effects
        effects_text = []
        for stat, value in trait_data['effects'].items():
            if isinstance(value, (int, float)) and abs(value) < 1:
                effects_text.append(f"{value:+.0%} {stat.title()}")
            else:
                effects_text.append(f"{value:+d} {stat.title()}")

        effects_str = ", ".join(effects_text) if effects_text else "No effects"

        # Display in requested format
        header = f"{trait_data['category_icon']} {trait_data['category_name']}: "

        if quality_text:
            print(f"📌 {rarity_text} {quality_text}")
            print(f"{header}{trait_data['name']} ({effects_str})")
        else:
            print(f"📌 {rarity_text}")
            print(f"{header}{trait_data['name']} ({effects_str})")

        # Display magic elements if present
        if trait_data.get('magic_elements'):
            elements_display = []
            for element in trait_data['magic_elements']:
                element_data = MAGIC_ELEMENTS[element]
                elements_display.append(f"{element_data['icon']} {element_data['name']}")
            print(f"🔮 Elemental Affinities: {', '.join(elements_display)}")

        print("─" * 40)
    
    def evaluate_stat_quality(self, stat: str, value: int) -> str:
        """Evaluate if a stat value is positive, average, or negative"""
        # This is a simplified evaluation - could be enhanced with race-specific logic
        if value >= 50:
            return "Positive"
        elif value >= 30:
            return "Average"
        else:
            return "Negative"
    
    def generate_full_character(self, display: bool = True) -> Dict[str, Any]:
        """Generate complete character through all phases"""
        print("🌟 Beginning character generation...")
        print("⚠️  Remember: This is your one and only life. No rerolls.")
        
        time.sleep(1)
        
        # Phase 1: Race Generation
        race_data = self.generate_race()
        if display:
            self.display_race_reveal(race_data)
            time.sleep(2)
        
        # Phase 2: Gender Generation
        gender_data = self.generate_gender()
        if display:
            self.display_gender_reveal(gender_data)
            time.sleep(2)
        
        # Phase 3: DNA Trait Generation
        print("\n🧬 Analyzing genetic code...")
        time.sleep(1)
        
        dna_traits = self.generate_all_dna_traits()
        if display:
            for trait_data in dna_traits.values():
                self.display_dna_trait_reveal(trait_data)
                time.sleep(1)
        
        print("\n🎭 Character generation complete!")
        print("Your fate has been sealed...")
        
        return self.character_profile
