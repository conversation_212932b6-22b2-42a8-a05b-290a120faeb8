"""
Character Generation Logger for One Life Isekai
Logs detailed character generation process with timestamps
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, List


class CharacterLogger:
    def __init__(self):
        self.log_data = {
            'timestamp': datetime.now().isoformat(),
            'generation_steps': [],
            'final_character': None,
            'core_stats_calculation': []
        }
        self.log_filename = None
        
        # Create logs directory if it doesn't exist
        if not os.path.exists('logs'):
            os.makedirs('logs')
    
    def start_generation(self):
        """Start a new character generation log"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = f"logs/character_generation_{timestamp}.json"
        
        self.log_step("GENERATION_START", "Character generation process initiated", {
            'timestamp': self.log_data['timestamp'],
            'resolution': '1280x720'
        })
    
    def log_step(self, step_type: str, description: str, data: Dict[str, Any] = None):
        """Log a generation step"""
        step_entry = {
            'step_type': step_type,
            'description': description,
            'timestamp': datetime.now().isoformat(),
            'data': data or {}
        }
        self.log_data['generation_steps'].append(step_entry)
    
    def log_race_generation(self, race_data: Dict[str, Any]):
        """Log race generation details"""
        self.log_step("RACE_GENERATION", f"Generated race: {race_data['name']}", {
            'race_name': race_data['name'],
            'rarity': race_data['rarity'],
            'quality': race_data['quality'],
            'base_stats': race_data['base_stats'],
            'utility_stats': race_data['utility_stats'],
            'innate_traits': race_data.get('innate_traits', []),
            'bonus_chances': race_data.get('bonus_chances', {})
        })
    
    def log_gender_generation(self, gender_data: Dict[str, Any]):
        """Log gender generation details"""
        self.log_step("GENDER_GENERATION", f"Generated gender: {gender_data['name']}", {
            'gender_name': gender_data['name'],
            'icon': gender_data['icon'],
            'effects': gender_data['effects']
        })
    
    def log_dna_trait_generation(self, trait_data: Dict[str, Any]):
        """Log DNA trait generation details"""
        self.log_step("DNA_TRAIT_GENERATION", f"Generated {trait_data['category_name']}: {trait_data['name']}", {
            'category': trait_data['category'],
            'category_name': trait_data['category_name'],
            'trait_name': trait_data['name'],
            'rarity': trait_data['rarity'],
            'quality': trait_data['quality'],
            'effects': trait_data['effects'],
            'magic_elements': trait_data.get('magic_elements', [])
        })
    
    def log_core_stats_calculation(self, step_description: str, stats_before: Dict[str, Any], 
                                 stats_after: Dict[str, Any], modifier_source: str):
        """Log core stats calculation steps"""
        calculation_entry = {
            'step': step_description,
            'modifier_source': modifier_source,
            'stats_before': stats_before.copy(),
            'stats_after': stats_after.copy(),
            'changes': {}
        }
        
        # Calculate changes
        for stat in stats_after:
            if stat in stats_before:
                change = stats_after[stat] - stats_before[stat]
                if change != 0:
                    calculation_entry['changes'][stat] = change
            else:
                calculation_entry['changes'][stat] = stats_after[stat]
        
        self.log_data['core_stats_calculation'].append(calculation_entry)
    
    def log_name_generation(self, character_name: str):
        """Log character name generation"""
        self.log_step("NAME_GENERATION", f"Generated character name: {character_name}", {
            'character_name': character_name
        })
    
    def finalize_log(self, final_character: Dict[str, Any]):
        """Finalize the log with the complete character data"""
        self.log_data['final_character'] = final_character
        
        # Log final summary
        self.log_step("GENERATION_COMPLETE", "Character generation completed successfully", {
            'total_steps': len(self.log_data['generation_steps']),
            'final_core_stats': {
                stat: final_character['effective_stats'].get(stat, 0) 
                for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']
            },
            'total_traits': len(final_character.get('dna_traits', {})),
            'magic_affinity': final_character['effective_stats'].get('magic_affinity', 0),
            'estimated_lifespan': final_character['effective_stats'].get('estimated_lifespan', 0)
        })
        
        # Save to file
        self.save_log()
    
    def save_log(self):
        """Save the log to a JSON file"""
        if self.log_filename:
            try:
                with open(self.log_filename, 'w', encoding='utf-8') as f:
                    json.dump(self.log_data, f, indent=2, ensure_ascii=False, default=str)
                print(f"📝 Character generation log saved: {self.log_filename}")
            except Exception as e:
                print(f"❌ Failed to save log: {e}")
    
    def get_log_summary(self) -> str:
        """Get a formatted summary of the generation log"""
        summary_lines = []
        summary_lines.append("📝 CHARACTER GENERATION LOG SUMMARY")
        summary_lines.append("=" * 50)
        summary_lines.append(f"Timestamp: {self.log_data['timestamp']}")
        summary_lines.append(f"Total Steps: {len(self.log_data['generation_steps'])}")
        summary_lines.append("")
        
        # Show key steps
        for step in self.log_data['generation_steps']:
            if step['step_type'] in ['RACE_GENERATION', 'GENDER_GENERATION', 'NAME_GENERATION']:
                summary_lines.append(f"• {step['description']}")
        
        summary_lines.append("")
        summary_lines.append("Core Stats Calculation Steps:")
        for calc in self.log_data['core_stats_calculation']:
            if calc['changes']:
                changes_str = ", ".join([f"{stat}: {change:+d}" for stat, change in calc['changes'].items()])
                summary_lines.append(f"  {calc['step']}: {changes_str}")
        
        return "\n".join(summary_lines)
