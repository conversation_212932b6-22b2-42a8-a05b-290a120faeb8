"""
Modern MMO-style Character Sheet for One Life Isekai
WoW-inspired character sheet with equipment slots and detailed stats
"""

import pygame
import os
from typing import Dict, Any, Optional, Tuple
from utils.constants import COLORS, SCREEN_WIDTH, SCREEN_HEIGHT


class ModernCharacterSheet:
    def __init__(self, screen: pygame.Surface, fonts: Dict[str, pygame.font.Font]):
        self.screen = screen
        self.fonts = fonts
        
        # Colors
        self.bg_color = (25, 25, 35)  # Dark blue-gray
        self.panel_color = (40, 40, 50)  # Lighter panel
        self.border_color = (100, 100, 120)  # Border
        self.text_color = (220, 220, 220)  # Light text
        self.gold_color = (255, 215, 0)  # Gold
        self.green_color = (50, 205, 50)  # Positive
        self.red_color = (220, 20, 60)  # Negative
        self.blue_color = (100, 149, 237)  # Mana
        self.unreadable_color = (60, 60, 70)  # Unreadable text
        
        # Layout constants
        self.sheet_rect = pygame.Rect(50, 50, SCREEN_WIDTH - 100, SCREEN_HEIGHT - 100)
        self.equipment_width = 200
        self.stats_width = 300
        
        # Equipment slots positions (left side)
        self.equipment_slots = self._create_equipment_slots()
        
        # Character data
        self.character_data = None
        self.unreadable_lines = []
        self.readable_lines = []
        
        # Load placeholder images
        self._load_placeholder_images()
    
    def _create_equipment_slots(self) -> Dict[str, pygame.Rect]:
        """Create equipment slot rectangles"""
        slots = {}
        slot_size = 40
        spacing = 45
        
        # Left side equipment layout
        eq_x = self.sheet_rect.x + 20
        eq_y = self.sheet_rect.y + 150
        
        # Equipment slots layout (similar to WoW)
        slots['helmet'] = pygame.Rect(eq_x + spacing, eq_y, slot_size, slot_size)
        slots['shoulder'] = pygame.Rect(eq_x, eq_y + spacing, slot_size, slot_size)
        slots['chest'] = pygame.Rect(eq_x + spacing, eq_y + spacing, slot_size, slot_size)
        slots['legs'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 2, slot_size, slot_size)
        slots['belt'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 3, slot_size, slot_size)
        slots['boots'] = pygame.Rect(eq_x + spacing, eq_y + spacing * 4, slot_size, slot_size)
        slots['gloves'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing, slot_size, slot_size)
        slots['main_hand'] = pygame.Rect(eq_x, eq_y + spacing * 2, slot_size, slot_size)
        slots['off_hand'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing * 2, slot_size, slot_size)
        slots['ring1'] = pygame.Rect(eq_x, eq_y + spacing * 3, slot_size, slot_size)
        slots['ring2'] = pygame.Rect(eq_x + spacing * 2, eq_y + spacing * 3, slot_size, slot_size)
        slots['necklace'] = pygame.Rect(eq_x + spacing, eq_y - spacing, slot_size, slot_size)
        
        return slots
    
    def _load_placeholder_images(self):
        """Load placeholder images for missing assets"""
        self.placeholder_images = {}
        
        # Create simple colored rectangles as placeholders
        slot_size = 40
        
        # Purple cube for traits/skills
        purple_surface = pygame.Surface((slot_size, slot_size))
        purple_surface.fill((128, 0, 128))
        pygame.draw.rect(purple_surface, (160, 32, 160), purple_surface.get_rect(), 2)
        self.placeholder_images['trait'] = purple_surface
        self.placeholder_images['skill'] = purple_surface
        
        # Gray slot for equipment
        gray_surface = pygame.Surface((slot_size, slot_size))
        gray_surface.fill((60, 60, 60))
        pygame.draw.rect(gray_surface, (100, 100, 100), gray_surface.get_rect(), 2)
        self.placeholder_images['equipment'] = gray_surface
        
        # Character portrait placeholder
        portrait_surface = pygame.Surface((100, 100))
        portrait_surface.fill((80, 80, 80))
        pygame.draw.rect(portrait_surface, (120, 120, 120), portrait_surface.get_rect(), 3)
        self.placeholder_images['portrait'] = portrait_surface
    
    def load_character_image(self, race: str, gender: str) -> pygame.Surface:
        """Load character portrait image"""
        # Try to load specific race/gender image
        image_path = f"pictures/gender/{race}/{gender}.png"
        if os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Try race-only image
        race_path = f"pictures/race/{race}.png"
        if os.path.exists(race_path):
            try:
                image = pygame.image.load(race_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Use unknown placeholder
        unknown_path = "pictures/race/unknown.png"
        if os.path.exists(unknown_path):
            try:
                image = pygame.image.load(unknown_path)
                return pygame.transform.scale(image, (100, 100))
            except:
                pass
        
        # Return placeholder
        return self.placeholder_images['portrait']
    
    def initialize_character_sheet(self, character_data: Dict[str, Any]):
        """Initialize the character sheet with unreadable text"""
        self.character_data = character_data
        
        # Create unreadable lines (fantasy script style)
        self.unreadable_lines = []
        self.readable_lines = []
        
        # Generate mystical unreadable text for each stat line
        mystical_chars = "ᚠᚢᚦᚨᚱᚲᚷᚹᚺᚾᛁᛃᛇᛈᛉᛊᛏᛒᛖᛗᛚᛜᛞᛟ"
        
        for i in range(25):  # Number of stat lines
            unreadable_text = ''.join([mystical_chars[i % len(mystical_chars)] for _ in range(15 + i % 10)])
            self.unreadable_lines.append(unreadable_text)
            self.readable_lines.append("")  # Will be filled during generation
    
    def reveal_stat_line(self, line_index: int, readable_text: str):
        """Reveal a specific stat line with animation"""
        if 0 <= line_index < len(self.readable_lines):
            self.readable_lines[line_index] = readable_text
    
    def draw_character_sheet(self, reveal_progress: float = 0.0):
        """Draw the complete character sheet"""
        # Clear background
        self.screen.fill(self.bg_color)
        
        # Draw main panel
        pygame.draw.rect(self.screen, self.panel_color, self.sheet_rect)
        pygame.draw.rect(self.screen, self.border_color, self.sheet_rect, 3)
        
        if self.character_data:
            self._draw_character_header()
            self._draw_equipment_slots()
            self._draw_character_portrait()
            self._draw_vital_stats()
            self._draw_base_stats()
            self._draw_additional_stats()
            self._draw_traits_and_skills()
            self._draw_mystical_text(reveal_progress)
    
    def _draw_character_header(self):
        """Draw character name, class, level at the top"""
        header_y = self.sheet_rect.y + 20
        
        # Character name (placeholder for now)
        name_text = self.fonts['large'].render("NO NAME", True, self.gold_color)
        name_rect = name_text.get_rect(centerx=self.sheet_rect.centerx, y=header_y)
        self.screen.blit(name_text, name_rect)
        
        # Class and level (placeholders)
        class_text = self.fonts['medium'].render("Adventurer", True, self.text_color)
        level_text = self.fonts['medium'].render("Level 1", True, self.text_color)
        
        class_rect = class_text.get_rect(centerx=self.sheet_rect.centerx - 60, y=header_y + 40)
        level_rect = level_text.get_rect(centerx=self.sheet_rect.centerx + 60, y=header_y + 40)
        
        self.screen.blit(class_text, class_rect)
        self.screen.blit(level_text, level_rect)
    
    def _draw_equipment_slots(self):
        """Draw equipment slots on the left side"""
        for slot_name, slot_rect in self.equipment_slots.items():
            # Draw slot background
            pygame.draw.rect(self.screen, (30, 30, 40), slot_rect)
            pygame.draw.rect(self.screen, self.border_color, slot_rect, 2)
            
            # Draw placeholder equipment
            self.screen.blit(self.placeholder_images['equipment'], slot_rect)
    
    def _draw_character_portrait(self):
        """Draw character portrait"""
        if self.character_data:
            race = self.character_data.get('race', {}).get('name', 'unknown').lower()
            gender = self.character_data.get('gender', {}).get('name', 'unknown').lower()
            
            portrait = self.load_character_image(race, gender)
            portrait_rect = pygame.Rect(self.sheet_rect.x + self.equipment_width + 20, 
                                      self.sheet_rect.y + 80, 100, 100)
            
            self.screen.blit(portrait, portrait_rect)
            pygame.draw.rect(self.screen, self.border_color, portrait_rect, 3)
    
    def _draw_vital_stats(self):
        """Draw HP, MP, XP bars"""
        if not self.character_data:
            return
            
        stats = self.character_data.get('effective_stats', {})
        
        # Calculate values (placeholder calculations)
        max_hp = stats.get('VIT', 20) * 10 + 100
        current_hp = max_hp
        max_mp = stats.get('WILL', 20) * 5 + 50
        current_mp = max_mp
        current_xp = 0
        max_xp = 1000
        
        # Position next to portrait
        bars_x = self.sheet_rect.x + self.equipment_width + 140
        bars_y = self.sheet_rect.y + 100
        bar_width = 200
        bar_height = 20
        
        # HP Bar
        self._draw_stat_bar(bars_x, bars_y, bar_width, bar_height, 
                           current_hp, max_hp, self.red_color, "HP")
        
        # MP Bar
        self._draw_stat_bar(bars_x, bars_y + 30, bar_width, bar_height,
                           current_mp, max_mp, self.blue_color, "MP")
        
        # XP Bar
        self._draw_stat_bar(bars_x, bars_y + 60, bar_width, bar_height,
                           current_xp, max_xp, self.gold_color, "XP")
    
    def _draw_stat_bar(self, x: int, y: int, width: int, height: int, 
                      current: int, maximum: int, color: Tuple[int, int, int], label: str):
        """Draw a stat bar with current/max values"""
        # Background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(self.screen, (20, 20, 20), bg_rect)
        pygame.draw.rect(self.screen, self.border_color, bg_rect, 2)
        
        # Fill bar
        if maximum > 0:
            fill_width = int((current / maximum) * (width - 4))
            fill_rect = pygame.Rect(x + 2, y + 2, fill_width, height - 4)
            pygame.draw.rect(self.screen, color, fill_rect)
        
        # Text
        text = f"{label}: {current}/{maximum}"
        text_surface = self.fonts['small'].render(text, True, self.text_color)
        text_rect = text_surface.get_rect(center=bg_rect.center)
        self.screen.blit(text_surface, text_rect)
    
    def _draw_mystical_text(self, reveal_progress: float):
        """Draw mystical unreadable text that gradually becomes readable"""
        if not self.character_data:
            return
            
        text_x = self.sheet_rect.x + self.equipment_width + 20
        text_y = self.sheet_rect.y + 200
        line_height = 20
        
        revealed_lines = int(reveal_progress * len(self.unreadable_lines))
        
        for i, (unreadable, readable) in enumerate(zip(self.unreadable_lines, self.readable_lines)):
            y_pos = text_y + i * line_height
            
            if i < revealed_lines and readable:
                # Show readable text
                text_surface = self.fonts['small'].render(readable, True, self.text_color)
                self.screen.blit(text_surface, (text_x, y_pos))
            else:
                # Show unreadable mystical text
                text_surface = self.fonts['small'].render(unreadable, True, self.unreadable_color)
                self.screen.blit(text_surface, (text_x, y_pos))

    def _draw_base_stats(self):
        """Draw base stats in two columns"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})
        base_stats = ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']

        # Position below vital stats
        stats_x = self.sheet_rect.x + self.equipment_width + 20
        stats_y = self.sheet_rect.y + 350
        col_width = 100

        for i, stat in enumerate(base_stats):
            col = i % 2
            row = i // 2
            x = stats_x + col * col_width
            y = stats_y + row * 25

            value = stats.get(stat, 0)
            text = f"{stat}: {value}"
            color = self._get_stat_color(value)

            text_surface = self.fonts['medium'].render(text, True, color)
            self.screen.blit(text_surface, (x, y))

    def _draw_additional_stats(self):
        """Draw additional stats like Age, Immunity, BMI"""
        if not self.character_data:
            return

        stats = self.character_data.get('effective_stats', {})

        # Position on the right side
        add_stats_x = self.sheet_rect.x + self.equipment_width + 250
        add_stats_y = self.sheet_rect.y + 200

        # Age (placeholder)
        age_text = f"Age: 18"
        age_surface = self.fonts['medium'].render(age_text, True, self.text_color)
        self.screen.blit(age_surface, (add_stats_x, add_stats_y))

        # Immunity
        immunity = stats.get('immune_system', 100)
        immunity_text = f"Immunity: {immunity}"
        immunity_surface = self.fonts['medium'].render(immunity_text, True, self.text_color)
        self.screen.blit(immunity_surface, (add_stats_x, add_stats_y + 30))

        # BMI (calculated from height and estimated weight)
        height_cm = stats.get('height', 170)
        estimated_weight = 70  # Placeholder
        bmi = estimated_weight / ((height_cm / 100) ** 2)
        bmi_text = f"BMI: {bmi:.1f}"
        bmi_surface = self.fonts['medium'].render(bmi_text, True, self.text_color)
        self.screen.blit(bmi_surface, (add_stats_x, add_stats_y + 60))

    def _draw_traits_and_skills(self):
        """Draw traits and skills with purple cube icons"""
        if not self.character_data:
            return

        # Traits section
        traits_x = self.sheet_rect.x + self.equipment_width + 20
        traits_y = self.sheet_rect.y + 450

        traits_title = self.fonts['medium'].render("Traits:", True, self.gold_color)
        self.screen.blit(traits_title, (traits_x, traits_y))

        # Draw trait icons (purple cubes for now)
        dna_traits = self.character_data.get('dna_traits', {})
        trait_count = 0
        for i, (category, trait) in enumerate(dna_traits.items()):
            if trait_count < 9:  # Limit display
                icon_x = traits_x + (trait_count % 3) * 45
                icon_y = traits_y + 30 + (trait_count // 3) * 45

                icon_rect = pygame.Rect(icon_x, icon_y, 40, 40)
                self.screen.blit(self.placeholder_images['trait'], icon_rect)
                trait_count += 1

        # Skills section (placeholder)
        skills_x = traits_x + 150
        skills_title = self.fonts['medium'].render("Skills:", True, self.gold_color)
        self.screen.blit(skills_title, (skills_x, traits_y))

        # Draw skill icons (purple cubes for now)
        for i in range(6):  # Placeholder skills
            icon_x = skills_x + (i % 3) * 45
            icon_y = traits_y + 30 + (i // 3) * 45

            icon_rect = pygame.Rect(icon_x, icon_y, 40, 40)
            self.screen.blit(self.placeholder_images['skill'], icon_rect)

    def _get_stat_color(self, value: int) -> Tuple[int, int, int]:
        """Get color based on stat value"""
        if value >= 70:
            return self.green_color
        elif value >= 40:
            return self.text_color
        else:
            return self.red_color

    def update_readable_stats(self, character_data: Dict[str, Any]):
        """Update the readable stat lines based on character data"""
        self.readable_lines = []

        # Race info
        race = character_data.get('race', {})
        self.readable_lines.append(f"Race: {race.get('name', 'Unknown')} ({race.get('rarity', 'common')})")

        # Gender info
        gender = character_data.get('gender', {})
        self.readable_lines.append(f"Gender: {gender.get('name', 'Unknown')}")

        # Base stats
        stats = character_data.get('effective_stats', {})
        for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']:
            value = stats.get(stat, 0)
            self.readable_lines.append(f"{stat}: {value}")

        # DNA Traits
        dna_traits = character_data.get('dna_traits', {})
        for category, trait in dna_traits.items():
            trait_line = f"{trait['category_name']}: {trait['name']} ({trait['rarity']})"
            self.readable_lines.append(trait_line)

        # Additional stats
        self.readable_lines.append(f"Magic Affinity: {stats.get('magic_affinity', 0)}")
        self.readable_lines.append(f"Beauty: {stats.get('beauty', 0)}")
        self.readable_lines.append(f"Height: {stats.get('height', 170)} cm")
        self.readable_lines.append(f"Lifespan: {stats.get('estimated_lifespan', 80)} years")

        # Pad with empty lines if needed
        while len(self.readable_lines) < 25:
            self.readable_lines.append("")
