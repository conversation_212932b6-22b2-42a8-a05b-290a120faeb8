"""
Trait definitions for One Life Isekai
Passive traits affecting character stats and abilities
"""

# Disability Traits
DISABILITY_TRAITS = {
    'blind': {
        'name': 'Blind',
        'description': 'Cannot see, severely limiting perception and combat effectiveness',
        'category': 'disability',
        'effects': {
            'perception': -80,
            'hit_chance': -50,
            'beauty': -20
        },
        'flags': ['is_disabled', 'vision_impaired'],
        'rarity': 'rare'
    },
    'deaf': {
        'name': 'Deaf',
        'description': 'Cannot hear, affecting social interactions and awareness',
        'category': 'disability',
        'effects': {
            'perception': -30,
            'persuasion': -25
        },
        'flags': ['is_disabled', 'hearing_impaired'],
        'rarity': 'uncommon'
    },
    'lame': {
        'name': 'Lame',
        'description': 'Mobility impairment affecting movement and physical activities',
        'category': 'disability',
        'effects': {
            'SPE': -40,
            'DEX': -20,
            'load_capacity': -30
        },
        'flags': ['is_disabled', 'mobility_impaired'],
        'rarity': 'uncommon'
    },
    'mute': {
        'name': 'Mute',
        'description': 'Cannot speak, severely limiting social interactions',
        'category': 'disability',
        'effects': {
            'persuasion': -60,
            'beauty': -10
        },
        'flags': ['is_disabled', 'speech_impaired'],
        'rarity': 'rare'
    },
    'frail': {
        'name': 'Frail',
        'description': 'Weak constitution and fragile body',
        'category': 'disability',
        'effects': {
            'STR': -25,
            'VIT': -30,
            'immune_system': -40
        },
        'flags': ['is_disabled', 'physically_weak'],
        'rarity': 'common'
    }
}

# Perk Traits (Positive)
PERK_TRAITS = {
    'third_eye_opened': {
        'name': 'Third Eye Opened',
        'description': 'Mystical sight allowing perception beyond the physical realm',
        'category': 'perk',
        'effects': {
            'perception': 50,
            'magic_affinity': 30,
            'INT': 20
        },
        'conditions': ['requires_trauma_or_training'],
        'flags': ['is_blessed', 'has_magic_sight'],
        'rarity': 'legendary'
    },
    'blessed': {
        'name': 'Blessed',
        'description': 'Favored by divine forces, bringing luck and protection',
        'category': 'perk',
        'effects': {
            'holy_resist': 25,
            'chaos_resist': 15,
            'beauty': 20,
            'persuasion': 15
        },
        'flags': ['is_blessed', 'divine_favor'],
        'rarity': 'very_rare'
    },
    'nimble_fingers': {
        'name': 'Nimble Fingers',
        'description': 'Exceptional manual dexterity and fine motor control',
        'category': 'perk',
        'effects': {
            'DEX': 15,
            'critical_hit_chance': 10
        },
        'flags': ['skilled_hands'],
        'rarity': 'uncommon'
    },
    'iron_will': {
        'name': 'Iron Will',
        'description': 'Unbreakable mental fortitude and determination',
        'category': 'perk',
        'effects': {
            'WILL': 25,
            'chaos_resist': 20,
            'persuasion': 10
        },
        'flags': ['strong_minded'],
        'rarity': 'rare'
    },
    'natural_beauty': {
        'name': 'Natural Beauty',
        'description': 'Exceptional physical attractiveness that opens doors',
        'category': 'perk',
        'effects': {
            'beauty': 40,
            'persuasion': 20
        },
        'flags': ['naturally_attractive'],
        'rarity': 'rare'
    },
    'giant_blood': {
        'name': 'Giant Blood',
        'description': 'Ancestral giant heritage granting size and strength',
        'category': 'perk',
        'effects': {
            'STR': 30,
            'height': 20,
            'load_capacity': 40,
            'weight': 25
        },
        'flags': ['giant_heritage', 'large_frame'],
        'rarity': 'very_rare'
    },
    'keen_intellect': {
        'name': 'Keen Intellect',
        'description': 'Superior reasoning and learning capabilities',
        'category': 'perk',
        'effects': {
            'INT': 25,
            'iq': 30,
            'magic_affinity': 15
        },
        'flags': ['genius_level'],
        'rarity': 'rare'
    }
}

# Mutation Traits
MUTATION_TRAITS = {
    'arcane_blood': {
        'name': 'Arcane Blood',
        'description': 'Magic-infused bloodline granting power at a cost',
        'category': 'mutation',
        'effects': {
            'magic_affinity': 40,
            'INT': 20,
            'WILL': 15,
            'immune_system': -20,
            'estimated_lifespan': -10
        },
        'flags': ['is_mutated', 'has_magic_core', 'unstable_genetics'],
        'rarity': 'very_rare'
    },
    'chaos_touch': {
        'name': 'Chaos Touch',
        'description': 'Corrupted by chaotic forces, gaining dark power',
        'category': 'mutation',
        'effects': {
            'chaos_bonus': 30,
            'chaos_resist': 40,
            'holy_resist': -30,
            'beauty': -25,
            'persuasion': -15
        },
        'flags': ['is_mutated', 'is_cursed', 'chaos_touched'],
        'rarity': 'rare'
    },
    'elemental_affinity': {
        'name': 'Elemental Affinity',
        'description': 'Natural connection to elemental forces',
        'category': 'mutation',
        'effects': {
            'fire_bonus': 20,
            'water_bonus': 20,
            'earth_bonus': 20,
            'wind_bonus': 20,
            'magic_affinity': 25
        },
        'flags': ['is_mutated', 'elemental_touched'],
        'rarity': 'rare'
    },
    'draconic_heritage': {
        'name': 'Draconic Heritage',
        'description': 'Ancient dragon blood flowing through veins',
        'category': 'mutation',
        'effects': {
            'STR': 20,
            'INT': 15,
            'fire_resist': 30,
            'magic_affinity': 20,
            'beauty': 10,
            'estimated_lifespan': 50
        },
        'flags': ['is_mutated', 'dragon_blood', 'long_lived'],
        'rarity': 'legendary'
    },
    'shadow_touched': {
        'name': 'Shadow Touched',
        'description': 'Marked by shadow realm, gaining stealth abilities',
        'category': 'mutation',
        'effects': {
            'DEX': 20,
            'perception': 15,
            'chaos_resist': 25,
            'holy_resist': -20,
            'beauty': -15
        },
        'flags': ['is_mutated', 'shadow_marked'],
        'rarity': 'rare'
    }
}

# Condition Traits (Diseases, Curses, etc.)
CONDITION_TRAITS = {
    'disease_carrier': {
        'name': 'Disease Carrier',
        'description': 'Harbors infectious diseases that can spread to others',
        'category': 'condition',
        'effects': {
            'immune_system': -30,
            'beauty': -20,
            'persuasion': -25
        },
        'flags': ['is_diseased', 'contagious'],
        'duration': 'permanent',
        'rarity': 'uncommon'
    },
    'cursed_blood': {
        'name': 'Cursed Blood',
        'description': 'Ancient curse flowing through bloodline',
        'category': 'condition',
        'effects': {
            'chaos_resist': -20,
            'holy_resist': -15,
            'estimated_lifespan': -20,
            'beauty': -15
        },
        'flags': ['is_cursed', 'hereditary_curse'],
        'duration': 'permanent',
        'rarity': 'rare'
    },
    'fated_death': {
        'name': 'Fated Death',
        'description': 'Destiny has marked this soul for an early grave',
        'category': 'condition',
        'effects': {
            'estimated_lifespan': -40,
            'perception': 20,
            'WILL': 15
        },
        'conditions': ['triggers_prophetic_dreams'],
        'flags': ['is_fated', 'death_marked'],
        'duration': 'permanent',
        'rarity': 'very_rare'
    },
    'plague_survivor': {
        'name': 'Plague Survivor',
        'description': 'Survived a deadly plague, gaining immunity but bearing scars',
        'category': 'condition',
        'effects': {
            'immune_system': 40,
            'VIT': 10,
            'beauty': -30,
            'STR': -10
        },
        'flags': ['plague_immune', 'scarred'],
        'duration': 'permanent',
        'rarity': 'uncommon'
    },
    'madness': {
        'name': 'Madness',
        'description': 'Mind fractured by trauma or forbidden knowledge',
        'category': 'condition',
        'effects': {
            'INT': -20,
            'persuasion': -40,
            'magic_affinity': 25,
            'perception': 15
        },
        'conditions': ['unpredictable_behavior'],
        'flags': ['is_mad', 'unstable_mind'],
        'duration': 'permanent',
        'rarity': 'rare'
    },
    'vampiric_thirst': {
        'name': 'Vampiric Thirst',
        'description': 'Cursed with an unnatural hunger for blood',
        'category': 'condition',
        'effects': {
            'STR': 15,
            'DEX': 10,
            'VIT': -20,
            'holy_resist': -40,
            'chaos_resist': 20,
            'estimated_lifespan': 200
        },
        'conditions': ['requires_blood_consumption'],
        'flags': ['is_cursed', 'undead_touched', 'blood_dependent'],
        'duration': 'permanent',
        'rarity': 'legendary'
    }
}

# Latent Traits (Hidden until triggered)
LATENT_TRAITS = {
    'berserker_rage': {
        'name': 'Berserker Rage',
        'description': 'Hidden fury that emerges in desperate combat',
        'category': 'latent',
        'effects': {
            'STR': 40,
            'DEX': 20,
            'VIT': 30,
            'INT': -30,
            'persuasion': -50
        },
        'triggers': ['low_health', 'extreme_stress', 'ally_death'],
        'flags': ['is_latent', 'combat_triggered'],
        'duration': 'temporary',
        'rarity': 'rare'
    },
    'prophetic_dreams': {
        'name': 'Prophetic Dreams',
        'description': 'Visions of possible futures during sleep',
        'category': 'latent',
        'effects': {
            'perception': 30,
            'INT': 15,
            'WILL': -10
        },
        'triggers': ['high_stress', 'near_death', 'magical_exposure'],
        'flags': ['is_latent', 'prophetic', 'sleep_dependent'],
        'duration': 'episodic',
        'rarity': 'very_rare'
    },
    'magical_awakening': {
        'name': 'Magical Awakening',
        'description': 'Dormant magical potential waiting to be unlocked',
        'category': 'latent',
        'effects': {
            'magic_affinity': 50,
            'INT': 20,
            'WILL': 25
        },
        'triggers': ['magical_exposure', 'extreme_emotion', 'near_death'],
        'flags': ['is_latent', 'magic_potential'],
        'duration': 'permanent_once_triggered',
        'rarity': 'rare'
    }
}

# Trait Rarity Weights (for random generation)
TRAIT_RARITY_WEIGHTS = {
    'common': 100,
    'uncommon': 50,
    'rare': 20,
    'very_rare': 5,
    'legendary': 1
}

# Trait Categories for AI Logic
TRAIT_CATEGORIES = {
    'positive': ['perk'],
    'negative': ['disability', 'condition'],
    'neutral': ['mutation'],
    'hidden': ['latent'],
    'genetic': ['perk', 'mutation', 'disability'],
    'acquired': ['condition'],
    'magical': ['mutation', 'latent'],
    'physical': ['disability', 'perk'],
    'mental': ['condition', 'latent']
}

# Trait Flags for Complex Interactions
TRAIT_FLAGS = {
    'is_cursed': ['cursed_blood', 'chaos_touch', 'vampiric_thirst'],
    'is_blessed': ['blessed', 'third_eye_opened'],
    'is_mutated': ['arcane_blood', 'chaos_touch', 'elemental_affinity', 'draconic_heritage', 'shadow_touched'],
    'is_disabled': ['blind', 'deaf', 'lame', 'mute', 'frail'],
    'is_fated': ['fated_death'],
    'has_magic_core': ['arcane_blood'],
    'is_latent': ['berserker_rage', 'prophetic_dreams', 'magical_awakening']
}

# All traits combined for easy access
ALL_TRAITS = {
    **DISABILITY_TRAITS,
    **PERK_TRAITS,
    **MUTATION_TRAITS,
    **CONDITION_TRAITS,
    **LATENT_TRAITS
}
