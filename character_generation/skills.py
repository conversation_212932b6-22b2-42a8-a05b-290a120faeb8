"""
Skill definitions for One Life Isekai
Learnable abilities and passive skills
"""

# Combat Skills
COMBAT_SKILLS = {
    'swordsmanship': {
        'name': 'Swordsmanship',
        'description': 'Mastery of blade weapons and sword techniques',
        'type': 'combat',
        'max_level': 10,
        'stat_requirements': {'STR': 10, 'DEX': 15},
        'effects_per_level': {
            'melee_physical_bonus': 5,
            'critical_hit_chance': 2,
            'hit_chance': 3
        },
        'tags': ['weapon_mastery', 'melee'],
        'rarity': 'common'
    },
    'archery': {
        'name': 'Archery',
        'description': 'Precision shooting with bows and crossbows',
        'type': 'combat',
        'max_level': 10,
        'stat_requirements': {'DEX': 20, 'perception': 15},
        'effects_per_level': {
            'ranged_physical_bonus': 6,
            'critical_hit_chance': 3,
            'hit_chance': 4
        },
        'tags': ['weapon_mastery', 'ranged'],
        'rarity': 'common'
    },
    'defense': {
        'name': 'Defense',
        'description': 'Blocking, parrying, and damage mitigation techniques',
        'type': 'combat',
        'max_level': 10,
        'stat_requirements': {'VIT': 15, 'DEX': 10},
        'effects_per_level': {
            'physical_blunt_resist': 3,
            'physical_slash_resist': 3,
            'physical_pierce_resist': 3
        },
        'tags': ['defensive', 'survival'],
        'rarity': 'common'
    },
    'stealth': {
        'name': 'Stealth',
        'description': 'Moving unseen and striking from shadows',
        'type': 'combat',
        'max_level': 10,
        'stat_requirements': {'DEX': 20, 'perception': 10},
        'effects_per_level': {
            'critical_damage': 8,
            'critical_hit_chance': 4
        },
        'special_abilities': ['backstab', 'invisibility'],
        'tags': ['stealth', 'assassination'],
        'rarity': 'uncommon'
    },
    'dual_wielding': {
        'name': 'Dual Wielding',
        'description': 'Fighting effectively with two weapons simultaneously',
        'type': 'combat',
        'max_level': 8,
        'stat_requirements': {'DEX': 25, 'STR': 15},
        'prerequisites': ['swordsmanship:3'],
        'effects_per_level': {
            'atk_speed': 0.1,
            'melee_physical_bonus': 3,
            'critical_hit_chance': 2
        },
        'tags': ['weapon_mastery', 'advanced'],
        'rarity': 'rare'
    },
    'berserker_combat': {
        'name': 'Berserker Combat',
        'description': 'Reckless fighting style trading defense for overwhelming offense',
        'type': 'combat',
        'max_level': 7,
        'stat_requirements': {'STR': 20, 'VIT': 15},
        'effects_per_level': {
            'melee_physical_bonus': 8,
            'atk_speed': 0.15,
            'physical_blunt_resist': -2,
            'physical_slash_resist': -2,
            'physical_pierce_resist': -2
        },
        'tags': ['berserker', 'high_risk'],
        'rarity': 'rare'
    }
}

# Magic Skills
MAGIC_SKILLS = {
    'pyromancy': {
        'name': 'Pyromancy',
        'description': 'Manipulation and creation of fire through magical means',
        'type': 'magic',
        'max_level': 10,
        'stat_requirements': {'INT': 20, 'magic_affinity': 15},
        'effects_per_level': {
            'fire_bonus': 8,
            'fire_resist': 5,
            'magic_cast_speed': 0.05
        },
        'spells_unlocked': ['fireball', 'flame_shield', 'inferno'],
        'tags': ['elemental', 'destructive'],
        'rarity': 'uncommon'
    },
    'water_shaping': {
        'name': 'Water Shaping',
        'description': 'Control over water and ice for offense and utility',
        'type': 'magic',
        'max_level': 10,
        'stat_requirements': {'INT': 18, 'magic_affinity': 12},
        'effects_per_level': {
            'water_bonus': 7,
            'water_resist': 6,
            'magic_cast_speed': 0.04
        },
        'spells_unlocked': ['ice_shard', 'healing_spring', 'tidal_wave'],
        'tags': ['elemental', 'utility'],
        'rarity': 'uncommon'
    },
    'necromancy': {
        'name': 'Necromancy',
        'description': 'Dark magic dealing with death, undeath, and soul manipulation',
        'type': 'magic',
        'max_level': 12,
        'stat_requirements': {'INT': 25, 'magic_affinity': 20, 'WILL': 20},
        'effects_per_level': {
            'chaos_bonus': 10,
            'chaos_resist': 4,
            'holy_resist': -3
        },
        'spells_unlocked': ['animate_dead', 'soul_drain', 'death_touch'],
        'tags': ['forbidden', 'chaos', 'death'],
        'social_penalties': {'persuasion': -5, 'beauty': -3},
        'rarity': 'very_rare'
    },
    'spellcraft': {
        'name': 'Spellcraft',
        'description': 'General magical theory and spell creation',
        'type': 'magic',
        'max_level': 15,
        'stat_requirements': {'INT': 30, 'iq': 120, 'magic_affinity': 25},
        'effects_per_level': {
            'magic_cast_speed': 0.08,
            'all_magic_bonus': 2
        },
        'special_abilities': ['create_spell', 'modify_spell', 'dispel_magic'],
        'tags': ['theoretical', 'advanced', 'creation'],
        'rarity': 'legendary'
    },
    'divine_magic': {
        'name': 'Divine Magic',
        'description': 'Holy magic channeled through faith and divine connection',
        'type': 'magic',
        'max_level': 10,
        'stat_requirements': {'WILL': 25, 'magic_affinity': 15},
        'trait_requirements': ['blessed'],
        'effects_per_level': {
            'holy_bonus': 9,
            'holy_resist': 6,
            'chaos_resist': 3
        },
        'spells_unlocked': ['heal', 'divine_shield', 'smite'],
        'tags': ['divine', 'healing', 'protection'],
        'rarity': 'rare'
    }
}

# Crafting Skills
CRAFTING_SKILLS = {
    'blacksmithing': {
        'name': 'Blacksmithing',
        'description': 'Forging weapons, armor, and metal tools',
        'type': 'crafting',
        'max_level': 10,
        'stat_requirements': {'STR': 15, 'iq': 80},
        'effects_per_level': {
            'crafting_quality': 10,
            'fire_resist': 2
        },
        'crafting_unlocks': ['iron_sword', 'steel_armor', 'masterwork_blade'],
        'tags': ['metalworking', 'equipment'],
        'rarity': 'common'
    },
    'alchemy': {
        'name': 'Alchemy',
        'description': 'Brewing potions, creating magical compounds, and transmutation',
        'type': 'crafting',
        'max_level': 12,
        'stat_requirements': {'INT': 20, 'iq': 100, 'magic_affinity': 10},
        'effects_per_level': {
            'potion_effectiveness': 15,
            'poison_resist': 3
        },
        'crafting_unlocks': ['healing_potion', 'strength_elixir', 'philosophers_stone'],
        'tags': ['chemistry', 'magic', 'consumables'],
        'rarity': 'uncommon'
    },
    'herbalism': {
        'name': 'Herbalism',
        'description': 'Knowledge of plants, their properties, and medicinal uses',
        'type': 'crafting',
        'max_level': 8,
        'stat_requirements': {'perception': 15, 'iq': 70},
        'effects_per_level': {
            'herb_gathering_efficiency': 20,
            'poison_resist': 4,
            'immune_system': 2
        },
        'crafting_unlocks': ['antidote', 'healing_salve', 'energy_tea'],
        'tags': ['nature', 'medicine', 'gathering'],
        'rarity': 'common'
    },
    'enchanting': {
        'name': 'Enchanting',
        'description': 'Imbuing items with magical properties and effects',
        'type': 'crafting',
        'max_level': 10,
        'stat_requirements': {'INT': 25, 'magic_affinity': 20, 'iq': 110},
        'prerequisites': ['spellcraft:3'],
        'effects_per_level': {
            'enchantment_power': 12,
            'magic_cast_speed': 0.03
        },
        'crafting_unlocks': ['magic_weapon', 'protection_amulet', 'ring_of_power'],
        'tags': ['magic', 'enhancement', 'advanced'],
        'rarity': 'rare'
    }
}

# Social Skills
SOCIAL_SKILLS = {
    'persuasion': {
        'name': 'Persuasion',
        'description': 'Art of convincing others through logical argument and charisma',
        'type': 'social',
        'max_level': 10,
        'stat_requirements': {'persuasion': 20, 'iq': 90},
        'effects_per_level': {
            'persuasion': 5,
            'negotiation_success': 8
        },
        'tags': ['communication', 'influence'],
        'rarity': 'common'
    },
    'deception': {
        'name': 'Deception',
        'description': 'Lying, misdirection, and manipulation of truth',
        'type': 'social',
        'max_level': 10,
        'stat_requirements': {'iq': 85, 'DEX': 15},
        'effects_per_level': {
            'lie_detection_resist': 10,
            'stealth_bonus': 3
        },
        'social_penalties': {'persuasion': -2},
        'tags': ['manipulation', 'criminal'],
        'rarity': 'uncommon'
    },
    'seduction': {
        'name': 'Seduction',
        'description': 'Using physical and emotional attraction to influence others',
        'type': 'social',
        'max_level': 8,
        'stat_requirements': {'beauty': 30, 'persuasion': 15},
        'effects_per_level': {
            'beauty': 3,
            'persuasion': 2,
            'influence_opposite_gender': 12
        },
        'tags': ['attraction', 'manipulation'],
        'rarity': 'uncommon'
    },
    'leadership': {
        'name': 'Leadership',
        'description': 'Inspiring and commanding others in various situations',
        'type': 'social',
        'max_level': 12,
        'stat_requirements': {'persuasion': 25, 'WILL': 20, 'iq': 100},
        'effects_per_level': {
            'group_morale_bonus': 8,
            'persuasion': 3,
            'command_effectiveness': 10
        },
        'tags': ['command', 'inspiration'],
        'rarity': 'rare'
    },
    'intimidation': {
        'name': 'Intimidation',
        'description': 'Using fear and presence to control others',
        'type': 'social',
        'max_level': 8,
        'stat_requirements': {'STR': 20, 'WILL': 15},
        'effects_per_level': {
            'fear_effect': 12,
            'persuasion': -1,
            'beauty': -2
        },
        'tags': ['fear', 'dominance'],
        'rarity': 'common'
    }
}

# Survival Skills
SURVIVAL_SKILLS = {
    'foraging': {
        'name': 'Foraging',
        'description': 'Finding edible plants, fungi, and natural resources',
        'type': 'survival',
        'max_level': 8,
        'stat_requirements': {'perception': 15, 'iq': 60},
        'effects_per_level': {
            'food_finding_efficiency': 20,
            'poison_identification': 15
        },
        'tags': ['gathering', 'nature'],
        'rarity': 'common'
    },
    'hunting': {
        'name': 'Hunting',
        'description': 'Tracking, trapping, and killing wild animals for food',
        'type': 'survival',
        'max_level': 10,
        'stat_requirements': {'perception': 20, 'DEX': 15, 'STR': 10},
        'effects_per_level': {
            'tracking_ability': 15,
            'ranged_physical_bonus': 3,
            'meat_yield': 12
        },
        'tags': ['tracking', 'combat', 'food'],
        'rarity': 'common'
    },
    'cooking': {
        'name': 'Cooking',
        'description': 'Preparing nutritious and beneficial meals from raw ingredients',
        'type': 'survival',
        'max_level': 8,
        'stat_requirements': {'iq': 70, 'perception': 10},
        'effects_per_level': {
            'food_effectiveness': 18,
            'poison_resist': 2
        },
        'tags': ['food', 'health'],
        'rarity': 'common'
    },
    'wilderness_survival': {
        'name': 'Wilderness Survival',
        'description': 'Thriving in harsh natural environments',
        'type': 'survival',
        'max_level': 10,
        'stat_requirements': {'VIT': 15, 'perception': 15, 'iq': 80},
        'effects_per_level': {
            'environmental_resist': 5,
            'shelter_building': 12,
            'weather_prediction': 10
        },
        'tags': ['environment', 'adaptation'],
        'rarity': 'uncommon'
    },
    'first_aid': {
        'name': 'First Aid',
        'description': 'Basic medical treatment and wound care',
        'type': 'survival',
        'max_level': 8,
        'stat_requirements': {'iq': 85, 'DEX': 12},
        'effects_per_level': {
            'healing_effectiveness': 15,
            'disease_treatment': 10
        },
        'tags': ['medicine', 'healing'],
        'rarity': 'common'
    }
}

# Passive Skills (Always Active)
PASSIVE_SKILLS = {
    'sharp_mind': {
        'name': 'Sharp Mind',
        'description': 'Enhanced cognitive processing and mental clarity',
        'type': 'passive',
        'max_level': 5,
        'stat_requirements': {'iq': 110},
        'effects_per_level': {
            'INT': 3,
            'iq': 5,
            'learning_speed': 10
        },
        'tags': ['mental', 'learning'],
        'rarity': 'rare'
    },
    'endurance': {
        'name': 'Endurance',
        'description': 'Superior physical stamina and resistance to fatigue',
        'type': 'passive',
        'max_level': 8,
        'stat_requirements': {'VIT': 20},
        'effects_per_level': {
            'VIT': 2,
            'fatigue_resist': 12,
            'load_capacity': 5
        },
        'tags': ['physical', 'stamina'],
        'rarity': 'uncommon'
    },
    'arcane_instinct': {
        'name': 'Arcane Instinct',
        'description': 'Intuitive understanding of magical forces and phenomena',
        'type': 'passive',
        'max_level': 6,
        'stat_requirements': {'magic_affinity': 25, 'INT': 20},
        'effects_per_level': {
            'magic_affinity': 4,
            'spell_learning_speed': 15,
            'magic_detection': 20
        },
        'tags': ['magic', 'intuition'],
        'rarity': 'rare'
    },
    'iron_stomach': {
        'name': 'Iron Stomach',
        'description': 'Resistance to poison, disease, and poor food',
        'type': 'passive',
        'max_level': 5,
        'stat_requirements': {'VIT': 18},
        'effects_per_level': {
            'poison_resist': 8,
            'disease_resist': 10,
            'food_tolerance': 20
        },
        'tags': ['health', 'resistance'],
        'rarity': 'uncommon'
    },
    'keen_senses': {
        'name': 'Keen Senses',
        'description': 'Heightened sensory perception and awareness',
        'type': 'passive',
        'max_level': 6,
        'stat_requirements': {'perception': 25},
        'effects_per_level': {
            'perception': 5,
            'danger_sense': 15,
            'hidden_detection': 12
        },
        'tags': ['senses', 'awareness'],
        'rarity': 'uncommon'
    }
}

# Latent Skills (Hidden until unlocked)
LATENT_SKILLS = {
    'battle_trance': {
        'name': 'Battle Trance',
        'description': 'Enter a focused combat state that enhances all abilities',
        'type': 'latent',
        'max_level': 3,
        'stat_requirements': {'STR': 25, 'WILL': 20},
        'unlock_conditions': ['survive_near_death_in_combat', 'kill_10_enemies'],
        'effects_per_level': {
            'all_combat_stats': 15,
            'pain_resist': 30
        },
        'duration': 'temporary',
        'tags': ['combat', 'enhancement'],
        'rarity': 'very_rare'
    },
    'magical_sight': {
        'name': 'Magical Sight',
        'description': 'Ability to perceive magical auras and hidden enchantments',
        'type': 'latent',
        'max_level': 4,
        'stat_requirements': {'magic_affinity': 30, 'perception': 20},
        'unlock_conditions': ['exposure_to_powerful_magic', 'third_eye_opened_trait'],
        'effects_per_level': {
            'magic_detection': 25,
            'illusion_resist': 20,
            'enchantment_identification': 30
        },
        'tags': ['magic', 'perception'],
        'rarity': 'legendary'
    },
    'death_defiance': {
        'name': 'Death Defiance',
        'description': 'Supernatural ability to survive mortal wounds',
        'type': 'latent',
        'max_level': 2,
        'stat_requirements': {'VIT': 30, 'WILL': 25},
        'unlock_conditions': ['survive_fatal_damage', 'fated_death_trait'],
        'effects_per_level': {
            'death_save_chance': 25,
            'critical_injury_resist': 40
        },
        'tags': ['survival', 'fate'],
        'rarity': 'legendary'
    }
}

# Skill Categories for AI Logic
SKILL_CATEGORIES = {
    'offensive': ['combat', 'magic'],
    'defensive': ['survival', 'passive'],
    'utility': ['crafting', 'social', 'survival'],
    'magical': ['magic'],
    'physical': ['combat', 'survival'],
    'mental': ['social', 'passive'],
    'advanced': ['latent'],
    'basic': ['combat', 'crafting', 'social', 'survival']
}

# Skill Learning Difficulty
SKILL_DIFFICULTY = {
    'trivial': ['cooking', 'foraging', 'first_aid'],
    'easy': ['swordsmanship', 'archery', 'persuasion', 'herbalism'],
    'moderate': ['stealth', 'pyromancy', 'blacksmithing', 'hunting'],
    'hard': ['necromancy', 'dual_wielding', 'leadership', 'alchemy'],
    'very_hard': ['spellcraft', 'enchanting', 'battle_trance'],
    'legendary': ['magical_sight', 'death_defiance']
}

# All skills combined for easy access
ALL_SKILLS = {
    **COMBAT_SKILLS,
    **MAGIC_SKILLS,
    **CRAFTING_SKILLS,
    **SOCIAL_SKILLS,
    **SURVIVAL_SKILLS,
    **PASSIVE_SKILLS,
    **LATENT_SKILLS
}
