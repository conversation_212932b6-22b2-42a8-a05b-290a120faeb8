"""
Stat definitions for One Life Isekai
Core character statistics, damage types, resistances, and utility stats
"""

# Base Primary Stats
BASE_STATS = {
    'STR': {
        'name': 'Strength',
        'description': 'Physical power affecting melee damage, HP, and carrying capacity',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'melee_damage': 1.0,
            'ranged_damage': 0.33,
            'hp': 0.5,
            'load_capacity': 1.0
        }
    },
    'DEX': {
        'name': 'Dexterity',
        'description': 'Agility and precision affecting accuracy, critical hits, and attack speed',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'melee_damage': 0.33,
            'ranged_damage': 1.0,
            'critical_hit_chance': 1.0,
            'hit_chance': 1.0,
            'atk_speed': 1.0
        }
    },
    'VIT': {
        'name': 'Vitality',
        'description': 'Life force determining health points and basic resistance',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'hp': 1.0,
            'all_resistances': 0.1
        }
    },
    'INT': {
        'name': 'Intelligence',
        'description': 'Mental capacity affecting magic damage and spell effectiveness',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'magic_damage': 1.0,
            'magic_skill_use': 1.0
        }
    },
    'SPE': {
        'name': 'Speed',
        'description': 'Movement velocity affecting travel and positioning',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'travel_speed': 1.0
        }
    },
    'WILL': {
        'name': 'Willpower',
        'description': 'Mental fortitude determining mana capacity and casting speed',
        'type': 'base',
        'is_percentage': False,
        'range': (1, 10000),
        'effects': {
            'mana': 1.0,
            'magic_cast_speed': 0.2
        }
    }
}

# Damage Stats (Flat Values)
DAMAGE_STATS = {
    'melee_physical': {
        'name': 'Melee Physical Damage',
        'description': 'Base unarmed and melee weapon damage',
        'type': 'damage',
        'is_percentage': False,
        'subtypes': ['blunt', 'slash', 'pierce']
    },
    'ranged_physical': {
        'name': 'Ranged Physical Damage',
        'description': 'Projectile and thrown weapon damage',
        'type': 'damage',
        'is_percentage': False,
        'subtypes': ['blunt', 'slash', 'pierce']
    },
    'fire': {
        'name': 'Fire Damage',
        'description': 'Elemental fire damage from magic or enchanted weapons',
        'type': 'damage',
        'is_percentage': False
    },
    'water': {
        'name': 'Water Damage',
        'description': 'Elemental water damage from magic or enchanted weapons',
        'type': 'damage',
        'is_percentage': False
    },
    'earth': {
        'name': 'Earth Damage',
        'description': 'Elemental earth damage from magic or enchanted weapons',
        'type': 'damage',
        'is_percentage': False
    },
    'wind': {
        'name': 'Wind Damage',
        'description': 'Elemental wind damage from magic or enchanted weapons',
        'type': 'damage',
        'is_percentage': False
    },
    'holy': {
        'name': 'Holy Damage',
        'description': 'Divine damage effective against undead and chaos',
        'type': 'damage',
        'is_percentage': False
    },
    'chaos': {
        'name': 'Chaos Damage',
        'description': 'Dark magic, poison, and corruption damage',
        'type': 'damage',
        'is_percentage': False
    }
}

# Combat Stats
COMBAT_STATS = {
    'critical_hit_chance': {
        'name': 'Critical Hit Chance',
        'description': 'Probability of dealing critical damage',
        'type': 'combat',
        'is_percentage': True,
        'cap': 95
    },
    'critical_damage': {
        'name': 'Critical Damage',
        'description': 'Additional damage multiplier on critical hits',
        'type': 'combat',
        'is_percentage': True
    },
    'hit_chance': {
        'name': 'Hit Chance',
        'description': 'Accuracy in combat encounters',
        'type': 'combat',
        'is_percentage': True,
        'cap': 95
    },
    'atk_speed': {
        'name': 'Attack Speed',
        'description': 'Rate of physical attacks per turn',
        'type': 'combat',
        'is_percentage': False
    },
    'magic_cast_speed': {
        'name': 'Magic Cast Speed',
        'description': 'Rate of spell casting and channeling',
        'type': 'combat',
        'is_percentage': False
    }
}

# Percentage Damage Bonuses
DAMAGE_BONUS_STATS = {
    'melee_physical_bonus': {
        'name': 'Melee Physical Damage %',
        'description': 'Percentage bonus to melee physical damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'ranged_physical_bonus': {
        'name': 'Ranged Physical Damage %',
        'description': 'Percentage bonus to ranged physical damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'fire_bonus': {
        'name': 'Fire Damage %',
        'description': 'Percentage bonus to fire damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'water_bonus': {
        'name': 'Water Damage %',
        'description': 'Percentage bonus to water damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'earth_bonus': {
        'name': 'Earth Damage %',
        'description': 'Percentage bonus to earth damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'wind_bonus': {
        'name': 'Wind Damage %',
        'description': 'Percentage bonus to wind damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'holy_bonus': {
        'name': 'Holy Damage %',
        'description': 'Percentage bonus to holy damage',
        'type': 'damage_bonus',
        'is_percentage': True
    },
    'chaos_bonus': {
        'name': 'Chaos Damage %',
        'description': 'Percentage bonus to chaos damage',
        'type': 'damage_bonus',
        'is_percentage': True
    }
}

# Resistance Stats (Maximum 90%)
RESISTANCE_STATS = {
    'physical_blunt_resist': {
        'name': 'Physical Blunt Resistance %',
        'description': 'Resistance to blunt physical damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'physical_slash_resist': {
        'name': 'Physical Slash Resistance %',
        'description': 'Resistance to slashing physical damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'physical_pierce_resist': {
        'name': 'Physical Pierce Resistance %',
        'description': 'Resistance to piercing physical damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'fire_resist': {
        'name': 'Fire Resistance %',
        'description': 'Resistance to fire damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'water_resist': {
        'name': 'Water Resistance %',
        'description': 'Resistance to water damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'earth_resist': {
        'name': 'Earth Resistance %',
        'description': 'Resistance to earth damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'wind_resist': {
        'name': 'Wind Resistance %',
        'description': 'Resistance to wind damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'holy_resist': {
        'name': 'Holy Resistance %',
        'description': 'Resistance to holy damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    },
    'chaos_resist': {
        'name': 'Chaos Resistance %',
        'description': 'Resistance to chaos damage',
        'type': 'resistance',
        'is_percentage': True,
        'cap': 90
    }
}

# Utility Stats
UTILITY_STATS = {
    'beauty': {
        'name': 'Beauty',
        'description': 'Physical attractiveness affecting social interactions and opportunities',
        'type': 'utility',
        'is_percentage': False,
        'range': (1, 10000),
        'tags': ['social', 'economic']
    },
    'perception': {
        'name': 'Perception',
        'description': 'Awareness and observation skills for detecting threats and opportunities',
        'type': 'utility',
        'is_percentage': False,
        'range': (1, 10000),
        'tags': ['survival', 'combat']
    },
    'persuasion': {
        'name': 'Persuasion',
        'description': 'Ability to influence others through speech and charisma',
        'type': 'utility',
        'is_percentage': False,
        'range': (1, 10000),
        'tags': ['social', 'economic']
    },
    'estimated_lifespan': {
        'name': 'Estimated Lifespan',
        'description': 'Expected years of life based on genetics and fate',
        'type': 'utility',
        'is_percentage': False,
        'range': (20, 10000),
        'immutable': True,
        'tags': ['fate', 'genetic']
    },
    'iq': {
        'name': 'Intelligence Quotient',
        'description': 'Learning capacity and problem-solving ability',
        'type': 'utility',
        'is_percentage': False,
        'range': (50, 10000),
        'tags': ['learning', 'genetic']
    },
    'height': {
        'name': 'Height',
        'description': 'Physical stature in centimeters',
        'type': 'utility',
        'is_percentage': False,
        'range': (20, 10000),
        'immutable': True,
        'tags': ['physical', 'genetic']
    },
    'magic_affinity': {
        'name': 'Magic Affinity',
        'description': 'Natural connection to magical forces',
        'type': 'utility',
        'is_percentage': False,
        'range': (0, 10000),
        'tags': ['magic', 'genetic']
    },
    'load_capacity': {
        'name': 'Load Capacity',
        'description': 'Maximum weight that can be carried without penalties',
        'type': 'utility',
        'is_percentage': False,
        'derived_from': ['STR', 'height'],
        'tags': ['physical', 'survival']
    },
    'immune_system': {
        'name': 'Immune System',
        'description': 'Resistance to disease and aging effects',
        'type': 'utility',
        'is_percentage': False,
        'range': (1, 10000),
        'tags': ['health', 'genetic']
    },
    'weight': {
        'name': 'Weight',
        'description': 'Body mass in kilograms',
        'type': 'utility',
        'is_percentage': False,
        'range': (10, 30000),
        'tags': ['physical', 'health']
    },
    'daily_calorie_requirement': {
        'name': 'Daily Calorie Requirement',
        'description': 'Calories needed per day to maintain current weight',
        'type': 'utility',
        'is_percentage': False,
        'derived_from': ['weight', 'height', 'STR'],
        'tags': ['health', 'survival']
    },
    'bmi': {
        'name': 'Body Mass Index',
        'description': 'Health indicator calculated from weight and height',
        'type': 'utility',
        'is_percentage': False,
        'derived_from': ['weight', 'height'],
        'range': (10, 50),
        'optimal_range': (18.5, 24.9),
        'tags': ['health']
    },
    'crafting_quality': {
        'name': 'Crafting Quality',
        'description': 'Skill in creating high-quality crafted items',
        'type': 'utility',
        'is_percentage': False,
        'range': (0, 100),
        'tags': ['crafting', 'skill']
    },
    'potion_effectiveness': {
        'name': 'Potion Effectiveness',
        'description': 'Bonus to the power and duration of consumed potions',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['alchemy', 'consumables']
    },
    'poison_resist': {
        'name': 'Poison Resistance',
        'description': 'Resistance to toxic substances and venoms',
        'type': 'utility',
        'is_percentage': True,
        'cap': 90,
        'tags': ['health', 'resistance']
    },
    'herb_gathering_efficiency': {
        'name': 'Herb Gathering Efficiency',
        'description': 'Skill in finding and harvesting medicinal plants',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['gathering', 'nature']
    },
    'enchantment_power': {
        'name': 'Enchantment Power',
        'description': 'Strength of magical enchantments placed on items',
        'type': 'utility',
        'is_percentage': False,
        'range': (0, 100),
        'tags': ['magic', 'crafting']
    },
    'negotiation_success': {
        'name': 'Negotiation Success',
        'description': 'Likelihood of successful bargaining and deal-making',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['social', 'economic']
    },
    'lie_detection_resist': {
        'name': 'Lie Detection Resistance',
        'description': 'Ability to avoid detection when deceiving others',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['deception', 'social']
    },
    'stealth_bonus': {
        'name': 'Stealth Bonus',
        'description': 'Additional effectiveness when moving unseen',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['stealth', 'infiltration']
    },
    'influence_opposite_gender': {
        'name': 'Opposite Gender Influence',
        'description': 'Enhanced persuasive power over the opposite gender',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['social', 'attraction']
    },
    'group_morale_bonus': {
        'name': 'Group Morale Bonus',
        'description': 'Ability to inspire and motivate groups of people',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['leadership', 'social']
    },
    'command_effectiveness': {
        'name': 'Command Effectiveness',
        'description': 'Skill in giving orders and directing others',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['leadership', 'military']
    },
    'fear_effect': {
        'name': 'Fear Effect',
        'description': 'Ability to instill fear and dread in others',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['intimidation', 'psychological']
    },
    'food_finding_efficiency': {
        'name': 'Food Finding Efficiency',
        'description': 'Skill in locating edible resources in the wild',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'foraging']
    },
    'poison_identification': {
        'name': 'Poison Identification',
        'description': 'Ability to recognize toxic substances and plants',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'knowledge']
    },
    'tracking_ability': {
        'name': 'Tracking Ability',
        'description': 'Skill in following trails and finding quarry',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['hunting', 'survival']
    },
    'meat_yield': {
        'name': 'Meat Yield',
        'description': 'Efficiency in butchering and processing game',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['hunting', 'food']
    },
    'food_effectiveness': {
        'name': 'Food Effectiveness',
        'description': 'Bonus nutrition and benefits from prepared meals',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['cooking', 'health']
    },
    'environmental_resist': {
        'name': 'Environmental Resistance',
        'description': 'Resistance to harsh weather and terrain effects',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'adaptation']
    },
    'shelter_building': {
        'name': 'Shelter Building',
        'description': 'Skill in constructing protective structures',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'construction']
    },
    'weather_prediction': {
        'name': 'Weather Prediction',
        'description': 'Ability to forecast weather patterns',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'knowledge']
    },
    'healing_effectiveness': {
        'name': 'Healing Effectiveness',
        'description': 'Skill in treating wounds and ailments',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['medicine', 'healing']
    },
    'disease_treatment': {
        'name': 'Disease Treatment',
        'description': 'Ability to cure and manage diseases',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['medicine', 'health']
    },
    'learning_speed': {
        'name': 'Learning Speed',
        'description': 'Rate at which new skills and knowledge are acquired',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['education', 'mental']
    },
    'fatigue_resist': {
        'name': 'Fatigue Resistance',
        'description': 'Resistance to exhaustion and tiredness',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['endurance', 'physical']
    },
    'spell_learning_speed': {
        'name': 'Spell Learning Speed',
        'description': 'Rate at which magical spells are mastered',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['magic', 'learning']
    },
    'magic_detection': {
        'name': 'Magic Detection',
        'description': 'Ability to sense magical auras and enchantments',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['magic', 'perception']
    },
    'disease_resist': {
        'name': 'Disease Resistance',
        'description': 'Resistance to infectious diseases and plagues',
        'type': 'utility',
        'is_percentage': True,
        'cap': 90,
        'tags': ['health', 'immunity']
    },
    'food_tolerance': {
        'name': 'Food Tolerance',
        'description': 'Ability to consume spoiled or unusual foods safely',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'health']
    },
    'danger_sense': {
        'name': 'Danger Sense',
        'description': 'Intuitive awareness of threats and hazards',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['survival', 'intuition']
    },
    'hidden_detection': {
        'name': 'Hidden Detection',
        'description': 'Ability to find concealed objects and passages',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['exploration', 'perception']
    },
    'all_combat_stats': {
        'name': 'All Combat Stats',
        'description': 'Bonus to all combat-related statistics',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['combat', 'enhancement']
    },
    'all_magic_bonus': {
        'name': 'All Magic Bonus',
        'description': 'Bonus to all magical damage types',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['magic', 'enhancement']
    },
    'pain_resist': {
        'name': 'Pain Resistance',
        'description': 'Resistance to pain and suffering effects',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['endurance', 'mental']
    },
    'illusion_resist': {
        'name': 'Illusion Resistance',
        'description': 'Resistance to magical illusions and mind tricks',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['mental', 'magic']
    },
    'enchantment_identification': {
        'name': 'Enchantment Identification',
        'description': 'Ability to identify magical properties of items',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['magic', 'knowledge']
    },
    'death_save_chance': {
        'name': 'Death Save Chance',
        'description': 'Probability of surviving otherwise fatal damage',
        'type': 'utility',
        'is_percentage': True,
        'cap': 75,
        'tags': ['survival', 'fate']
    },
    'critical_injury_resist': {
        'name': 'Critical Injury Resistance',
        'description': 'Resistance to permanent injuries and disabilities',
        'type': 'utility',
        'is_percentage': True,
        'tags': ['health', 'resilience']
    }
}

# Condition Modifiers (Percentage bonuses/penalties to base stats)
CONDITION_MODIFIERS = {
    'str_modifier': {
        'name': 'Strength Modifier %',
        'description': 'Temporary or permanent modifier to strength',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'STR'
    },
    'dex_modifier': {
        'name': 'Dexterity Modifier %',
        'description': 'Temporary or permanent modifier to dexterity',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'DEX'
    },
    'vit_modifier': {
        'name': 'Vitality Modifier %',
        'description': 'Temporary or permanent modifier to vitality',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'VIT'
    },
    'int_modifier': {
        'name': 'Intelligence Modifier %',
        'description': 'Temporary or permanent modifier to intelligence',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'INT'
    },
    'spe_modifier': {
        'name': 'Speed Modifier %',
        'description': 'Temporary or permanent modifier to speed',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'SPE'
    },
    'will_modifier': {
        'name': 'Willpower Modifier %',
        'description': 'Temporary or permanent modifier to willpower',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'WILL'
    }
}

# Utility Condition Modifiers
UTILITY_CONDITION_MODIFIERS = {
    'beauty_modifier': {
        'name': 'Beauty Modifier %',
        'description': 'Temporary or permanent modifier to beauty',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'beauty'
    },
    'perception_modifier': {
        'name': 'Perception Modifier %',
        'description': 'Temporary or permanent modifier to perception',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'perception'
    },
    'persuasion_modifier': {
        'name': 'Persuasion Modifier %',
        'description': 'Temporary or permanent modifier to persuasion',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'persuasion'
    },
    'lifespan_modifier': {
        'name': 'Lifespan Modifier %',
        'description': 'Permanent modifier to estimated lifespan',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'estimated_lifespan'
    },
    'iq_modifier': {
        'name': 'IQ Modifier %',
        'description': 'Temporary or permanent modifier to intelligence quotient',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'iq'
    },
    'magic_affinity_modifier': {
        'name': 'Magic Affinity Modifier %',
        'description': 'Temporary or permanent modifier to magic affinity',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'magic_affinity'
    },
    'load_capacity_modifier': {
        'name': 'Load Capacity Modifier %',
        'description': 'Temporary or permanent modifier to carrying capacity',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'load_capacity'
    },
    'immune_system_modifier': {
        'name': 'Immune System Modifier %',
        'description': 'Temporary or permanent modifier to disease resistance',
        'type': 'condition',
        'is_percentage': True,
        'affects': 'immune_system'
    }
}

# Stat Groups for AI Logic
STAT_GROUPS = {
    'combat': ['STR', 'DEX', 'VIT', 'critical_hit_chance', 'critical_damage', 'hit_chance', 'atk_speed'],
    'magic': ['INT', 'WILL', 'magic_affinity', 'magic_cast_speed'],
    'social': ['beauty', 'persuasion', 'iq'],
    'survival': ['VIT', 'perception', 'immune_system', 'load_capacity'],
    'physical': ['STR', 'DEX', 'VIT', 'SPE', 'height', 'weight'],
    'mental': ['INT', 'WILL', 'iq', 'magic_affinity'],
    'genetic': ['estimated_lifespan', 'height', 'iq', 'magic_affinity', 'immune_system']
}

# All stats combined for easy access
ALL_STATS = {
    **BASE_STATS,
    **DAMAGE_STATS,
    **COMBAT_STATS,
    **DAMAGE_BONUS_STATS,
    **RESISTANCE_STATS,
    **UTILITY_STATS,
    **CONDITION_MODIFIERS,
    **UTILITY_CONDITION_MODIFIERS
}
